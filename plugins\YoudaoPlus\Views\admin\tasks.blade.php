@extends('admin::layouts.master')

@section('title', __('YoudaoPlus::common.batch_translate'))

@section('content')
  <div class="alert alert-light">
    <span>{{ __('YoudaoPlus::common.disable') }}? <a href="{{ admin_route("plugins.edit",['code' => 'youdao_plus']) }}" target="_blank">{{ __('YoudaoPlus::common.enable') }}</a></span>
  </div>
  <div id="translation-app" class="card" v-cloak>
    <div class="card-body h-min-600">
      <div class="d-flex justify-content-between mb-4">
        <div class="top-left-btn">
          <button type="button" class="btn btn-primary me-3" @click="checkedCreate('add', null)" @if($run_job) disabled @endif><i class="bi bi-plus-square"></i> {{ __('YoudaoPlus::common.add_task') }}</button>
          <button type="button" class="btn btn-info text-white" @click="runTask()" @if($run_job) disabled @endif><i class="bi bi-play-circle"></i> {{  __('YoudaoPlus::common.run_task') }}</button>
        </div>

        <div class="top-right-btn">
          <button type="button" class="btn btn-outline-primary me-3" @if(!$run_job) disabled @endif @click="retryTask()"><i class="bi bi-arrow-repeat"></i> {{ __('YoudaoPlus::common.btn_retry') }}</button>
          <button type="button" class="btn btn-outline-primary" @if(!$run_job) disabled @endif @click="cleanTask()"><i class="bi bi-trash"></i> {{  __('YoudaoPlus::common.btn_clean') }}</button>
        </div>
      </div>
      <div class="table-push">
        <table class="table">
          <thead>
          <tr>
            <th>ID</th>
            <th>{{ __('YoudaoPlus::common.translation_type') }}</th>
            <th>{{ __('YoudaoPlus::common.translation_to') }}</th>
            <th>{{ __('YoudaoPlus::common.status') }}</th>
            <th>{{ __('YoudaoPlus::common.created_at') }}</th>
            <th>{{ __('YoudaoPlus::common.updated_at') }}</th>
            {{-- <th>{{ __('common.action') }}</th> --}}
          </tr>
          </thead>
          <tbody v-if="jobs.data.length">
          <tr v-for="job, index in jobs.data" :key="index">
            <td>@{{ job.id }}</td>
            <td>@{{ job.formatted_translate_type }}</td>
            <td>@{{ job.formatted_lang }}</td>
            <td>
              <div>
                <span v-if="job.status" :class="job.status ? 'badge text-bg-success' : 'badge text-bg-warning'">@{{ job.formatted_status}}</span>
                <div class="progress" style="width: 200px" role="progressbar" aria-label="Animated striped example" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" v-if="job.status == 0">
                  <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 75%">@{{ job.formatted_status}}</div>
                </div>
              </div>
            </td>
            <td>@{{ job.created_at }}</td>
            <td>@{{ job.updated_at }}</td>
            {{-- <td></td> --}}
          </tr>
          </tbody>
          <tbody v-else>
            <tr>
              <td colspan="7" class="border-0">
                <x-admin-no-data text="当前还没有任务" />
                <div class="d-flex justify-content-center">
                  <button type="button" class="btn btn-primary me-3" @click="checkedCreate('add', null)"><i class="bi bi-plus-square"></i> {{ __('YoudaoPlus::common.add_task') }}</button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <el-pagination v-if="jobs.data.length" :pager-count="5" layout="total, prev, pager, next" background :page-size="jobs.per_page" :current-page.sync="page"
                     :total="jobs.total"></el-pagination>
    </div>

    <el-dialog title="{{  __('YoudaoPlus::common.add_task') }}" :visible.sync="dialog.show" width="620px"
               @close="closeCustomersDialog('form')" :close-on-click-modal="false">
      <el-form ref="form" :rules="rules" :model="dialog.form" label-width="148px">
        <el-form-item label="{{  __('YoudaoPlus::common.translation_type') }}" prop="entity_type">
          <el-select v-model="dialog.form.entity_type" placeholder="{{ __('YoudaoPlus::common.translation_type') }}">
            <el-option label="{{ __('common.all') }}" value="all"></el-option>
            <el-option
              v-for="item in entities"
              :key="item.id"
              :label="item.name"
              :value="item.entity_type">
            </el-option>
          </el-select>
          <div style="font-size: 12px;color: #999;margin-top: -6px">{{ __('YoudaoPlus::common.translation_type_1') }}</div>
        </el-form-item>

        <el-form-item label="源语言">
          @foreach (locales() as $item)
            @if ($item['code'] == system_setting('base.locale'))
              {{ $item['name'] }}
            @endif
          @endforeach
          <span style="font-size: 12px;color: #999;margin-left: 6px">(源语言是取系统设置的默认语言，<a href="{{ admin_route('settings.index', ['tab' => 'tab-store', 'line' => 'locale']) }}" target="_blank">修改</a>)</span>
        </el-form-item>

        <el-form-item label="{{  __('YoudaoPlus::common.translation_to') }}" prop="to">
          <el-select v-model="dialog.form.to" multiple placeholder="{{  __('YoudaoPlus::common.translation_to') }}">
            <el-option label="{{ __('common.all') }}" value="all"></el-option>
            <el-option
              v-for="item in locales"
              :key="item.id"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
          <div style="font-size: 12px;color: #999;margin-top: -6px">{{ __('YoudaoPlus::common.translation_to_1') }}</div>
        </el-form-item>

        <el-form-item class="mt-5">
          @hook('admin.zones.index.content.dialog.btns.before')
          <el-button type="primary" @click="addFormSubmit('form')">{{ __('common.save') }}</el-button>
          <el-button @click="closeCustomersDialog('form')">{{ __('common.cancel') }}</el-button>
          @hook('admin.zones.index.content.dialog.btns.after')
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
@endsection

@push('footer')
  @include('admin::shared.vue-image')

  <script>
    var app = new Vue({
      el: '#translation-app',
      data: {
        jobs: @json($jobs ?? []),
        entities: @json($entities ?? []),
        locales: @json($locales ?? []),
        data_fields: @json($fields ?? []),
        fields: [],
        page: 1,
        dialog: {
          show: false,
          index: null,
          type: 'add',
          form: {
            id: null,
            from: '',
            to: 'all',
            entity_type: 'all',
            entity_fields: 'all',
          },
        },

        rules: {
          //from: [{required: true, message: '{{ __('YoudaoPlus::common.translation_from') }}', trigger: 'blur'}, ],
          to: [{required: true, message: '{{ __('YoudaoPlus::common.translation_to') }}', trigger: 'blur'}, ],
        },

        filter: {
        },

        url: '{{ admin_route("zones.index") }}',

        @hook('admin.zones.index.content.vue.data')
      },

      watch: {
        'dialog.form.entity_type'(newVal) {
          this.fields = this.data_fields[newVal];
        },
        page: function() {
          this.loadData();
        },
      },

      mounted() {
        if (this.jobs.data.length) {
          this.intervalLoadData();

          if (this.jobs.data.some(job => job.status == 0)) {
            $('.top-left-btn').find('button').attr('disabled', true);
            $('.top-right-btn').find('button').attr('disabled', false);
          } else {
            $('.top-left-btn').find('button').attr('disabled', false);
            $('.top-right-btn').find('button').attr('disabled', true);
          }
        }
      },

      methods: {
       async loadData(isQueryStatus = false) {
          let filter = {}
          Object.keys(this.filter).forEach(key => {
            if (this.filter[key]) {
              filter[key] = this.filter[key]
            }
          })

         await $http.get(`youdaoplus/tasks?page=${this.page}`, filter, {hload: isQueryStatus}).then((res) => {
            this.jobs = res.data.jobs;
          })
        },

        checkedCreate(type, index) {
          this.dialog.show = true
          this.dialog.type = type
          this.dialog.index = index

          if (type == 'edit') {
            this.dialog.form = JSON.parse(JSON.stringify(this.zones.data[index]));
          }
        },

        runTask() {
          const url = '{{ admin_route("youdaoplus.runTask") }}';
          if (!this.jobs.data.length || this.jobs.data.every(job => job.status == 1)) {
            this.$message.error('{{ __('YoudaoPlus::common.run_task_error') }}');
            return;
          }

          $http.get(url).then((res) => {
            this.$message.success('{{ __('YoudaoPlus::common.run_task_success') }}');
            window.location.reload();
          })
        },

        closeCustomersDialog(form) {
          this.$refs[form].resetFields();
          Object.keys(this.dialog.form).forEach(key => this.dialog.form[key] = '')
          this.dialog.show = false
        },

        addFormSubmit(form) {
          const url = '{{ admin_route("youdaoplus.addTask") }}';
          this.$refs[form].validate((valid) => {
            if (!valid) {
              this.$message.error('{{ __('common.error_form') }}');
              return;
            }

            $http['post'](url, this.dialog.form).then(async(res) => {
              await this.loadData();
              this.dialog.show = false
              $('.top-right-btn').find('button').attr('disabled', false);
              this.$confirm('任务添加成功，是否立即运行任务？', '提示', {
                confirmButtonText: '立即运行',
                cancelButtonText: '再等等',
                type: 'warning',
              }).then(() => {
                this.runTask();
              }).catch(() => {})
            })
          });
        },

        intervalLoadData() {
          var interval = setInterval(() => {
            var allDone = this.jobs.data.every(job => job.status == 1)
            if (allDone) {
              clearInterval(interval);
              $('.top-left-btn').find('button').attr('disabled', false);
              $('.top-right-btn').find('button').attr('disabled', true);
            }
            this.loadData(true);
          }, 2000);
        },

        retryTask() {
          const url = '{{ admin_route("youdaoplus.resetTask") }}';
          $http.get(url).then((res) => {
            this.$message.success('{{ __('YoudaoPlus::common.btn_retry_success') }}');
            window.location.reload();
          })
        },

        cleanTask() {
          const url = '{{ admin_route("youdaoplus.clearTask") }}';
          $http.get(url).then((res) => {
            this.$message.success('{{ __('YoudaoPlus::common.btn_clean_success') }}');
            window.location.reload();
          })
        },
      }
    })
  </script>
@endpush
