<header>
   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("header.before",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
  <div class="header-content d-none d-lg-block">
     <div class="nav-lang-current-cont">
         <?php
                    $__hook_name="header.top.currency";
                    ob_start();
                ?>
          <?php if(currencies()->count() > 1): ?>
            <div class="dropdown">
              <a class="btn ps-0" href="javascript:void(0)" role="button" id="currency-dropdown" data-toggle="dropdown"
                aria-expanded="false">
                <?php $__currentLoopData = currencies(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <?php if($currency->code == current_currency_code()): ?>
                    <?php if($currency->symbol_left): ?>
                    <?php echo e($currency->symbol_left); ?>

                    <?php endif; ?>
                    <?php echo e($currency->name); ?>

                    <?php if($currency->symbol_right): ?>
                    <?php echo e($currency->symbol_right); ?>

                    <?php endif; ?>
                  <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                 <i style="margin-left:5px" class="iconfont_one_cosmetics icon-xiala"></i>
              </a>
             
              <div class="dropdown-menu" aria-labelledby="currency-dropdown">
                <?php $__currentLoopData = currencies(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <a class="dropdown-item"
                    href="<?php echo e(shop_route('currency.switch', [$currency->code])); ?>">
                    <?php if($currency->symbol_left): ?>
                    <?php echo e($currency->symbol_left); ?>

                    <?php endif; ?>
                    <?php echo e($currency->name); ?>

                    <?php if($currency->symbol_right): ?>
                    <?php echo e($currency->symbol_right); ?>

                    <?php endif; ?>
                    </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </div>
            </div>
          <?php endif; ?>
           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>

           <?php
                    $__hook_name="header.top.language";
                    ob_start();
                ?>
          <?php if(count($languages) > 1): ?>
            <div class="dropdown">
              <a class="btn" href="javascript:void(0)" role="button" id="language-dropdown" data-toggle="dropdown"
                aria-expanded="false">
                <?php echo e(current_language()->name); ?>

                <i style="margin-left:5px" class="iconfont_one_cosmetics icon-xiala"></i>
              </a>

              <div class="dropdown-menu" aria-labelledby="language-dropdown">
                <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <a class="dropdown-item" href="<?php echo e(shop_route('lang.switch', [$language->code])); ?>">
                    <?php echo e($language->name); ?>

                  </a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </div>
            </div>
          <?php endif; ?>
           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
      </div>
    <div class="nav-cont">
     
      <div class="row align-items-center ">
        <div class="left d-flex align-items-center col-12 col-sm-12 col-md-12 col-lg-4">   
           <li class="nav-item"><a href="#offcanvas-search-top" data-bs-toggle="offcanvas"
                aria-controls="offcanvasExample" class="nav-link"><i class="iconfont_one_cosmetics icon-sousuo"></i></a></li>
        </div>
        <div class="col-md-4 col-lg-4">
           <?php
                    $__hook_name="header.menu.logo";
                    ob_start();
                ?>
          <div class="logo d-flex justify-content-center">
            <a href="<?php echo e(shop_route('home.index')); ?>"><img src="<?php echo e(image_origin(system_setting('base.logo'))); ?>" class="img-fluid"></a>
          </div>
           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
        </div>
        <div class="right-btn col-12 col-sm-12 col-md-4 navbar-expand-lg">
          <ul class="navbar-nav flex-row">
             <?php
                    $__hook_name="header.menu.icon";
                    ob_start();
                ?>
            <li class="nav-item"><a href="<?php echo e(shop_route('account.wishlist.index')); ?>" class="nav-link"><i
                  class="iconfont">&#xe662;</i></a></li>
            <li class="nav-item dropdown">
              <a href="<?php echo e(shop_route('account.index')); ?>" class="nav-link"><i class="iconfont">&#xe619;</i></a>
              <ul class="dropdown-menu dropdown-menu-end">
                <?php if(auth()->guard('web_shop')->check()): ?>
                  <li class="dropdown-item">
                    <h6 class="mb-0"><?php echo e(current_customer()->name); ?></h6>
                  </li>
                  <li>
                    <hr class="dropdown-divider opacity-100">
                  </li>
                  <li><a href="<?php echo e(shop_route('account.index')); ?>" class="dropdown-item"><i class="bi bi-person me-1"></i>
                    <?php echo e(__('shop/account.index')); ?></a></li>
                  <li><a href="<?php echo e(shop_route('account.order.index')); ?>" class="dropdown-item"><i
                        class="bi bi-clipboard-check me-1"></i> <?php echo e(__('shop/account/order.index')); ?></a></li>
                  <li><a href="<?php echo e(shop_route('account.wishlist.index')); ?>" class="dropdown-item"><i
                        class="bi bi-heart me-1"></i> <?php echo e(__('shop/account/wishlist.index')); ?></a></li>
                  <li>
                    <hr class="dropdown-divider opacity-100">
                  </li>
                  <li><a href="<?php echo e(shop_route('logout')); ?>" class="dropdown-item"><i class="bi bi-box-arrow-left me-1"></i>
                      <?php echo e(__('common.sign_out')); ?></a></li>
                <?php else: ?>
                  <li><a href="<?php echo e(shop_route('login.index')); ?>" class="dropdown-item"><i
                        class="bi bi-box-arrow-right me-1"></i><?php echo e(__('shop/login.login_and_sign')); ?></a></li>
                <?php endif; ?>
              </ul>
            </li>
             <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
            <li class="nav-item">
              <a class="nav-link position-relative btn-right-cart <?php echo e(equal_route('shop.carts.index') ? 'page-cart' : ''); ?>" href="javascript:void(0);" role="button">
                <i class="icon-24gl-bag iconfont_one_cosmetics"></i>
                <span class="cart-badge-quantity"></span>
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <div class="top-wrap d-lg-none">
    <div class="d-flex justify-content-between align-items-center px-2 bg-light">
      <div class="left d-flex align-items-center">
         <?php
                    $__hook_name="header.top.currency";
                    ob_start();
                ?>
        <?php if(currencies()->count() > 1): ?>
          <div class="dropdown">
            <a class="btn ps-0" href="javascript:void(0)" role="button" id="currency-dropdown" data-toggle="dropdown"
              aria-expanded="false">
              <?php $__currentLoopData = currencies(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($currency->code == current_currency_code()): ?>
                  <?php if($currency->symbol_left): ?>
                  <?php echo e($currency->symbol_left); ?>

                  <?php endif; ?>
                  <?php echo e($currency->name); ?>

                  <?php if($currency->symbol_right): ?>
                  <?php echo e($currency->symbol_right); ?>

                  <?php endif; ?>
                <?php endif; ?>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              <i style="margin-left:5px" class="iconfont_one_cosmetics icon-xiala"></i>
            </a>

            <div class="dropdown-menu" aria-labelledby="currency-dropdown">
              <?php $__currentLoopData = currencies(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a class="dropdown-item"
                  href="<?php echo e(shop_route('currency.switch', [$currency->code])); ?>">
                  <?php if($currency->symbol_left): ?>
                  <?php echo e($currency->symbol_left); ?>

                  <?php endif; ?>
                  <?php echo e($currency->name); ?>

                  <?php if($currency->symbol_right): ?>
                  <?php echo e($currency->symbol_right); ?>

                  <?php endif; ?>
                  </a>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
          </div>
        <?php endif; ?>
         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>

         <?php
                    $__hook_name="header.top.language";
                    ob_start();
                ?>
        <?php if(count($languages) > 1): ?>
          <div class="dropdown">
            <a class="btn" href="javascript:void(0)" role="button" id="language-dropdown" data-toggle="dropdown"
              aria-expanded="false">
              <?php echo e(current_language()->name); ?>

              <i style="margin-left:5px" class="iconfont_one_cosmetics icon-xiala"></i>
            </a>

            <div class="dropdown-menu" aria-labelledby="language-dropdown">
              <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a class="dropdown-item" href="<?php echo e(shop_route('lang.switch', [$language->code])); ?>">
                  <?php echo e($language->name); ?>

                </a>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
          </div>
        <?php endif; ?>
         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>

         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("header.top.left",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
      </div>

       <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("header.top.language.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>

      <div class="right nav">
        <?php if(system_setting('base.telephone', '')): ?>
           <?php
                    $__hook_name="header.top.telephone";
                    ob_start();
                ?>
          <div class="my-auto"><i class="bi bi-telephone-forward me-2"></i> <?php echo e(system_setting('base.telephone')); ?></div>
           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
        <?php endif; ?>

         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("header.top.right",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
      </div>
    </div>
  </div>

  <div class="navbar-expand-lg menu-box d-none d-lg-block">
    <div class="container">
      <div class="menu-wrap d-flex justify-content-center">
        <?php if(!is_mobile()): ?>
        <?php echo $__env->make('shared.menu-pc', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php endif; ?>
      </div>
    </div>
  </div>

  <div class="header-mobile d-lg-none">
    <div class="mobile-content">
      <div class="left">
        <div class="mobile-open-menu"><i class="bi bi-list"></i></div>
        <div class="mobile-open-search" href="#offcanvas-search-top" data-bs-toggle="offcanvas" aria-controls="offcanvasExample">
          <i class="iconfont">&#xe8d6;</i>
        </div>
      </div>
      <div class="center"><a href="<?php echo e(shop_route('home.index')); ?>">
          <img src="<?php echo e(image_origin(system_setting('base.logo'))); ?>" class="img-fluid"></a>
      </div>
      <div class="right">
        <a href="<?php echo e(shop_route('account.index')); ?>" class="nav-link mb-account-icon">
          <i class="iconfont">&#xe619;</i>
          <?php if(strstr(request()->route()->getName(), 'shop.account')): ?>
            <span></span>
          <?php endif; ?>
        </a>
        <a href="<?php echo e(shop_route('carts.index')); ?>" class="nav-link ms-3 m-cart position-relative"><i class="iconfont">&#xe634;</i> <span class="cart-badge-quantity"></span></a>
      </div>
    </div>
    </div>
  </div>
  <div class="offcanvas offcanvas-start" tabindex="-1" id="offcanvas-mobile-menu">
    <div class="offcanvas-header">
      <h5 class="offcanvas-title" id="offcanvasWithBothOptionsLabel"><?php echo e(__('common.menu')); ?></h5>
      <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body mobile-menu-wrap">
      <?php echo $__env->make('shared.menu-mobile', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    </div>
  </div>

  <?php if(!equal_route('shop.carts.index')): ?>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvas-right-cart" aria-labelledby="offcanvasRightLabel"></div>
  <?php endif; ?>

  <div class="offcanvas offcanvas-top" tabindex="-1" id="offcanvas-search-top" aria-labelledby="offcanvasTopLabel">
    <div class="offcanvas-header">
      <input type="text" class="form-control input-group-lg border-0 fs-4" focus
        placeholder="<?php echo e(__('common.input')); ?>" value="<?php echo e(request('keyword')); ?>" aria-label="Type your search here" aria-describedby="button-addon2">
      <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
  </div>

   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("header.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
</header>
<?php /**PATH D:\shopleadeCont\git\saas\themes\one_cosmetics/layout/header.blade.php ENDPATH**/ ?>