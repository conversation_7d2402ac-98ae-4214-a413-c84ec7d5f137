<?php echo plugin_setting('tiktok_pixel.code'); ?>



<script>
  const currency = '<?php echo e(current_currency_code()); ?>';

  ttq.identify({
    "email": '<?php echo e($email); ?>',
    "phone_number": '<?php echo e($phone_number); ?>',
    "external_id": '<?php echo e($external_id); ?>'
  });


  $(document).ready(function () {
    // 商品列表商品加 购物车 和 收藏
    $("button.btn-add-cart, button.btn-wishlist").click(function () {
      const id = $(this).attr('product-id');
      const price = $(this).attr('product-price');
      const productName = $(this).parents('.product-wrap').find('.product-name').text();
      const event = $(this).hasClass('btn-add-cart') ? 'AddToCart' : 'AddToWishlist';
      ttq.track(event, {
        "contents": [
          {
            "content_id": id,
            "content_type": "product",
            "content_name": productName
          }
        ],
        "value": price,
        "currency": currency
      });
    });

    // 商品详情加购物车
    $(".quantity-btns button.add-cart").click(function () {
      const id = $(this).attr('product-id');
      const price = $(this).attr('product-price');
      const productName = $('.product-name').text();
      ttq.track('AddToCart', {
        "contents": [
          {
            "content_id": id,
            "content_type": "product",
            "content_name": productName
          }
        ],
        "value": price,
        "currency": currency
      });
    });

    $(".offcanvas-header input").on("keyup", function (event) {
      if (event.which === 13) {
        ttq.track('Search', {
          "contents": [
            {
              "content_id": "",
              "content_type": "Search",
              "content_name": ""
            }
          ],
          "value": 0,
          "currency": currency,
          "query": $(this).val()
        });
      }
    });
  })
</script>
<?php /**PATH D:\shopleadeCont\git\saas\plugins/TikTokPixel/Views/layout_header_code.blade.php ENDPATH**/ ?>