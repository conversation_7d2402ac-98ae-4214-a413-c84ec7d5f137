<?php
/**
 * ForgottenController.php
 *
 * @copyright  2024 beikeshop.com - All Rights Reserved
 * @link       https://beikeshop.com
 * <AUTHOR> <<EMAIL>>
 * @created    2024-07-12 11:39:08
 * @modified   2024-07-12 11:39:08
 */

namespace Beike\SaasFront\Http\Controllers;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DevController
{

    public function __construct()
    {
        $this->logger = Log::build([
            'driver' => 'single',
            'path'   => storage_path('logs/paymeng_callback.log'),
        ]);
    }

    public function index(Request $request) {

        //test 123
        $script = '/www/wwwroot/dev.bash';
        $data = $request->all();
        Log::info("hook".json_encode($data));

        $branch = $data['ref'] ?? '';

        // 确保脚本具有执行权限
        if (is_executable($script)) {
            if ($branch === 'refs/heads/develop') {
                $output = shell_exec('sudo ' . $script . ' 2>&1');
                echo "<pre>$output</pre>";
            }
        } else {
            echo "脚本不可执行......";
        }
        return json_success('dev', $request->all());

    }



}
