@extends('layout.master')
@section('body-class', 'page-home')
@section('content')

<div class="modules-box" id="home-modules-box">

  @hook('home.modules.before')

  @foreach($modules as $module)
    @include($module['view_path'], $module)
  @endforeach

  @hook('home.modules.after')

</div>

@endsection

@push('add-scripts')
  <script>
$(document).ready(function () {
    function checkVisibility() {
        $('.module-item').not(':first').each(function () { // 排除第一个模块
            const elementTop = $(this).offset().top;
            const viewportBottom = $(window).scrollTop() + $(window).height();

            if (elementTop < viewportBottom - 50) { // 提前 50px 开始触发
                $(this).addClass('visible');
            }
        });
    }

    // 页面加载时检查一次
    checkVisibility();

    // 滚动时检查
    $(window).on('scroll', function () {
        checkVisibility();
    });
});

  </script>
@endpush
