<?php
/**
 * common.php
 *
 * @copyright  2024 beikeshop.com - All Rights Reserved
 * @link       https://beikeshop.com
 * <AUTHOR> <<EMAIL>>
 * @created    2024-05-13 16:19:06
 * @modified   2024-05-13 16:19:06
 */

return [
    'merchant_id' => 'Merchant ID',
    'md5_key' => 'Md5 Key',
    'api_key' => 'API Key',
    'api' => 'Sandbox/Live',
    'api_test' => 'Sandbox',
    'api_live' => 'Live',
    'log' => 'Log',
    'disable' => 'Disable',
    'enable' => 'Enable',
    'paying' => 'Paying',

    'card_number' => 'Card Number',
    'text_expiry' => 'Expiry',
    'cvv' => 'CVV',
    'expiry_year' => 'Year',
    'expiry_month' => 'Month',
    'expiry_year_month' => 'Month/Year',
    'holder_name' => 'Holder Name',
    'wdd' => 'wdd',


    'pay_submit_success'       => 'The payment request has been successfully submitted. Please check the payment result later.',
    'paid_success'             => 'You paid succeed!',

    'card_image' => 'Credit card Image',
    'payment_type' => 'Payment Type',
    'card' => 'Credit Card',
    'giropay' => 'Giropay',
    'bancontact' => 'Bancontact',
    'konbini' => 'Konbini',
    'grabpay' => 'Grabpay',
    'alfamart' => 'Alfamart',
    'safety_pay' => 'Safety Pay',
    'bancomat_pay' => 'Bancomat Pay',

    'card_type' => 'Card Type',
    'card_type_visa' => 'Visa',
    'card_type_mastercard' => 'Mastercard',
    'card_type_ae' => 'AE',
    'card_type_dc' => 'Discover',
    'card_type_jcb' => 'Jcb',
    'card_type_dclub' => 'DClub',
];
