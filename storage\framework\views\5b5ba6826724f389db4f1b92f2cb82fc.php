<?php echo plugin_setting('twitter_pixel.code'); ?>


<script>
  const currency = '<?php echo e(current_currency_code()); ?>';

  $(document).ready(function () {
    // 商品列表商品加 购物车 和 收藏
    $("button.btn-add-cart, button.btn-wishlist").click(function () {
      const id = $(this).attr('product-id');
      const price = $(this).attr('product-price');
      const productName = $(this).parents('.product-wrap').find('.product-name').text();
      const eventId = $(this).hasClass('btn-add-cart') ? '<?php echo e(plugin_setting('twitter_pixel.add_to_cart')); ?>' : '<?php echo e(plugin_setting('twitter_pixel.add_wishlist')); ?>';
      twq('event', eventId, {
        value: price,
        currency: currency,
        contents: [
          {
            content_type: 'product',
            content_id: id,
            content_name: productName,
            content_price: price,
            num_items: 1,
            content_group_id: null
          }]
      });
    });

    // 商品详情加购物车
    $(".quantity-btns button.add-cart").click(function () {
      const id = $(this).attr('product-id');
      const price = $(this).attr('product-price');
      const productName = $('.product-name').text();
      const quantity = $('input[name="quantity"]').val();
      twq('event', '<?php echo e(plugin_setting('twitter_pixel.add_to_cart')); ?>', {
        value: price,
        currency: currency,
        contents: [
          {
            content_type: 'product',
            content_id: id,
            content_name: productName,
            content_price: price,
            num_items: quantity,
            content_group_id: null
          }]
      });
    });

    $(".offcanvas-header input").on("keyup", function (event) {
      if (event.which === 13) {
        const searchValue = $(this).val();
        twq('event', '<?php echo e(plugin_setting('twitter_pixel.search')); ?>', {
          search_string: searchValue,
          value: 0,
          currency: currency
        });
      }
    });
    
  })
</script>
<?php /**PATH D:\shopleadeCont\git\saas\plugins/TwitterPixel/Views/layout_header_code.blade.php ENDPATH**/ ?>