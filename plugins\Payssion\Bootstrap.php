<?php

/**
 * Bootstrap.php
 *
 * @copyright  2023 beikeshop.com - All Rights Reserved
 * @link       https://beikeshop.com
 * <AUTHOR> <<EMAIL>>
 * @created    2023-08-17 15:15:27
 * @modified   2023-08-17 15:15:27
 */

namespace Plugin\Payssion;

use Beike\Repositories\OrderPaymentRepo;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use Stripe\Exception\ApiErrorException;

class Bootstrap
{
    /**
     * https://uniapp.dcloud.net.cn/tutorial/app-payment-paypal.html
     *
     * @throws ApiErrorException
     * @throws \Exception
     * @throws \Throwable
     */
    public function boot(): void
    {
        add_hook_filter('service.payment.mobile_pay.data', function ($data) {
            $order = $data['order'];
            if ($order->payment_method_code != 'paypal') {
                return $data;
            }

            $data['params'] = (new \Plugin\Payssion\Services\PayssionService($order))->getMobilePaymentData();

            return $data;
        });

        add_hook_filter('service.payment.pay.data', function ($data) {

            $paymentMethodCode = $data['order']['payment_method_code'];
            if (! Str::startsWith($paymentMethodCode, 'payssion')) {

                return $data;
            }


            $pm_id =  $data['payment_setting']['pm_id'] ?? 'pix_br';
            if ($data['payment_setting']['sandbox_mode'] == 1) {
                $api_key    = $data['payment_setting']['sandbox_api_key'];
                $secret_key = $data['payment_setting']['sandbox_secret_key'];
                $payAction  = 'http://sandbox.payssion.com/payment/create.html';
                $pm_id      = 'payssion_test';
            } else {
                $api_key    = $data['payment_setting']['api_key'];
                $secret_key = $data['payment_setting']['secret_key'];
                $payAction  = 'https://www.payssion.com/payment/create.html';
            }
            $order    = $data['order'];
            $amount   = floatval($order->total);
            $currency = $order->currency_code;
            $orderNo  = $order->number;

            $dataArr  = [$api_key, $pm_id, $amount, $currency, $orderNo, $secret_key];
            $msg      = implode('|', $dataArr);
            $api_sig  = md5($msg);
            $data     = [
                'order'         => $order,
                'pm_id'         => $pm_id,
                'order_number'         => $orderNo,
                'api_sig'       => $api_sig,
                'api_key'       => $api_key,
                'total'         => $amount,
                'currency_code' => $currency,
                'secret_key'    => $secret_key,
                'action'        => $payAction,
                'return_url'    => Request::getHttpHost().'/account/orders/'.$orderNo,
            ];
            $request = $data;
            unset($request['order']);

            OrderPaymentRepo::createOrUpdatePayment($order->id, ['request' => $data]);

            return $data;
        });
    }
}
