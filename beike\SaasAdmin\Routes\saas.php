<?php

use App\Http\Middleware\EncryptCookies;
use App\Http\Middleware\VerifyCsrfToken;
use Beike\SaasAdmin\Http\Controllers\ComboController;
use Beike\SaasAdmin\Http\Controllers\CommissionController;
use Beike\SaasAdmin\Http\Controllers\CouponController;
use Beike\SaasAdmin\Http\Controllers\CustomerActivityController;
use Beike\SaasAdmin\Http\Controllers\CustomerController;
use Beike\SaasAdmin\Http\Controllers\CustomerInfoFieldController;
use Beike\SaasAdmin\Http\Controllers\EditController;
use Beike\SaasAdmin\Http\Controllers\FileManagerController;
use Beike\SaasAdmin\Http\Controllers\HomeController;
use Beike\SaasAdmin\Http\Controllers\IssueController;
use Beike\SaasAdmin\Http\Controllers\LanguageController;
use Beike\SaasAdmin\Http\Controllers\LoggerController;
use Beike\SaasAdmin\Http\Controllers\LoginController;
use Beike\SaasAdmin\Http\Controllers\LogoutController;
use Beike\SaasAdmin\Http\Controllers\MarketingController;
use Beike\SaasAdmin\Http\Controllers\OrderController;
use Beike\SaasAdmin\Http\Controllers\PageCategoryController;
use Beike\SaasAdmin\Http\Controllers\VideoCategoryController;
use Beike\SaasAdmin\Http\Controllers\VideoController;
use Beike\SaasAdmin\Http\Controllers\PagesController;
use Beike\SaasAdmin\Http\Controllers\PartnerController;
use Beike\SaasAdmin\Http\Controllers\PersonController;
use Beike\SaasAdmin\Http\Controllers\PluginOrderController;
use Beike\SaasAdmin\Http\Controllers\ReportController;
use Beike\SaasAdmin\Http\Controllers\SaasRoleController;
use Beike\SaasAdmin\Http\Controllers\SaasUserController;
use Beike\SaasAdmin\Http\Controllers\ServerController;
use Beike\SaasAdmin\Http\Controllers\SettingController;
use Beike\SaasAdmin\Http\Controllers\StoreController;
use Beike\SaasAdmin\Http\Controllers\WebpageController;
use Beike\SaasAdmin\Http\Controllers\DesignMenuController;
use Beike\SaasAdmin\Middleware\SaasAuthenticate;
use Beike\SaasAdmin\Middleware\SetLocaleSaas;
use Beike\SaasAdmin\Models\SaasUser;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Route;
use Illuminate\View\Middleware\ShareErrorsFromSession;


if(isOnline()) {
    error_reporting(0);  // 关闭所有错误报告
}

Route::get('/app/public/qrcodes/{filename}', function ($filename) {
    $path = app()->storagePath('app/public/qrcodes/' . $filename);
    return response()->file($path);
});

Route::middleware([EncryptCookies::class, StartSession::class])->get('api/page/user_agreement', [WebpageController::class, 'get'])->name('webpages.get');


Route::prefix('saas')
    ->name('saas.')
    ->middleware([EncryptCookies::class, StartSession::class, ShareErrorsFromSession::class,SetLocaleSaas::class, VerifyCsrfToken::class])
    ->group(function () {
        Route::get('login', [LoginController::class, 'index'])->name('login.index');
        Route::post('login', [LoginController::class, 'store'])->name('login.store');
        Route::get('logout', [LogoutController::class, 'index'])->name('logout');

        Route::get('webpages/get/{id}', [WebpageController::class, 'get'])->name('webpages.get');


        Route::middleware(SaasAuthenticate::class . ':' . SaasUser::AUTH_GUARD)
            ->group(function () {
                Route::get('/', [HomeController::class, 'index'])->name('home.index');

                // 后台用户
                Route::middleware('can:saas_users_index')->get('saas_users', [SaasUserController::class, 'index'])->name('saas_users.index');
                Route::middleware('can:saas_users_create')->post('saas_users', [SaasUserController::class, 'store'])->name('saas_users.store');
                Route::middleware('can:saas_users_edit')->put('saas_users/{user_id}', [SaasUserController::class, 'update'])->name('saas_users.update');
                Route::middleware('can:saas_users_delete')->delete('saas_users/{user_id}', [SaasUserController::class, 'destroy'])->name('saas_users.destroy');
                // 后台角色
                Route::middleware('can:saas_roles_index')->get('saas_roles', [SaasRoleController::class, 'index'])->name('saas_roles.index');
                Route::middleware('can:saas_roles_create')->get('saas_roles/create', [SaasRoleController::class, 'create'])->name('saas_roles.create');
                Route::middleware('can:saas_roles_create')->post('saas_roles', [SaasRoleController::class, 'store'])->name('saas_roles.store');
                Route::middleware('can:saas_roles_show')->get('saas_roles/{role_id}/edit', [SaasRoleController::class, 'edit'])->name('saas_roles.edit');
                Route::middleware('can:saas_roles_edit')->put('saas_roles/{role_id}', [SaasRoleController::class, 'update'])->name('saas_roles.update');
                Route::middleware('can:saas_roles_delete')->delete('saas_roles/{role_id}', [SaasRoleController::class, 'destroy'])->name('saas_roles.destroy');

                Route::put('edit', [EditController::class, 'update'])->name('edit');
                Route::get('edit/locale', [EditController::class, 'locale'])->name('edit.locale');

                // 图片库
                Route::middleware('can:file_manager_show')->get('file_manager', [FileManagerController::class, 'index'])->name('file_manager.index');
                Route::middleware('can:file_manager_show')->get('file_manager/files', [FileManagerController::class, 'getFiles'])->name('file_manager.get_files');
                Route::middleware('can:file_manager_show')->get('file_manager/directories', [FileManagerController::class, 'getDirectories'])->name('file_manager.get_directories');
                Route::middleware('can:file_manager_create')->post('file_manager/directories', [FileManagerController::class, 'createDirectory'])->name('file_manager.create_directory');
                Route::middleware('can:file_manager_create')->post('file_manager/upload', [FileManagerController::class, 'uploadFiles'])->name('file_manager.upload');
                Route::middleware('can:file_manager_edit')->post('file_manager/rename', [FileManagerController::class, 'rename'])->name('file_manager.rename');
                Route::middleware('can:file_manager_delete')->delete('file_manager/files', [FileManagerController::class, 'destroyFiles'])->name('file_manager.delete_files');
                Route::middleware('can:file_manager_delete')->delete('file_manager/directories', [FileManagerController::class, 'destroyDirectories'])->name('file_manager.delete_directories');
                Route::middleware('can:file_manager_edit')->post('file_manager/move_directories', [FileManagerController::class, 'moveDirectories'])->name('file_manager.move_directories');
                Route::middleware('can:file_manager_edit')->post('file_manager/move_files', [FileManagerController::class, 'moveFiles'])->name('file_manager.move_files');
                Route::middleware('can:file_manager_show')->get('file_manager/export', [FileManagerController::class, 'exportZip'])->name('file_manager.export');

                // Customers
                Route::middleware('can:customers_index')->get('customers', [CustomerController::class, 'index'])->name('customers.index');
                Route::get('customers/select', [CustomerController::class, 'select'])->name('customers.select');
                Route::get('customers/email', [CustomerController::class, 'email'])->name('customers.email');

                Route::get('exchange/list', [\Beike\SaasAdmin\Http\Controllers\ExchangeController::class, 'list'])->name('exchange');
                Route::post('exchange/update', [\Beike\SaasAdmin\Http\Controllers\ExchangeController::class, 'update'])->name('exchange.update');

                Route::get('promo/userList', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'index'])->name('promo_index');
                Route::get('promo/user/order', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'userOrder'])->name('promo_user_order');
                Route::get('promo/user/order', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'userOrder'])->name('promo_user_order');
                Route::post('promo/user/orderUpdate', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'orderUpdate'])->name('promo.orderUpdate');
                Route::get('promo/order/list', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'orderList'])->name('promo_order_list');
                Route::get('promo/user/{id}', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'edit'])->name('promo.edit');
                Route::post('promo/user/update', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'update'])->name('promo.user_add');
                Route::get('promo/{id}/login', [\Beike\SaasAdmin\Http\Controllers\PromoController::class, 'login'])->name('promo.login');


                Route::middleware('can:customers_create')->get('customers/create', [CustomerController::class, 'create'])->name('customers.create');
                Route::middleware('can:customers_show')->get('customers/{id}/invite_code', [CustomerController::class, 'inviteCode'])->name('customers.inviteCode');
                Route::middleware('can:customers_show')->get('customers/autocomplete', [CustomerController::class, 'autocomplete'])->name('customers.autocomplete');
                Route::middleware('can:customers_create')->post('customers', [CustomerController::class, 'store'])->name('customers.store');
                Route::middleware('can:customers_show')->get('customers/{id}/edit', [CustomerController::class, 'edit'])->name('customers.edit');
                Route::middleware('can:customers_edit')->get('customers/{id}/login', [CustomerController::class, 'login'])->name('customers.login');
                Route::middleware('can:customers_edit')->put('customers/{id}', [CustomerController::class, 'update'])->name('customers.update');
                Route::middleware('can:customers_delete')->delete('customers/{id}', [CustomerController::class, 'destroy'])->name('customers.destroy');
                // Customer Activities
                Route::middleware('can:customer_activities_index')->get('customer_activities', [CustomerActivityController::class, 'index'])->name('customer_activities.index');
                // Settings
                Route::middleware('can:settings_index')->get('settings', [SettingController::class, 'index'])->name('settings.index');
                Route::middleware('can:settings_edit')->post('settings', [SettingController::class, 'store'])->name('settings.store');

                // CustomerInfoField
                Route::middleware('can:customer_info_fields_index')->get('customer_info_fields', [CustomerInfoFieldController::class, 'index'])->name('customer_info_fields.index');
                Route::middleware('can:customer_info_fields_create')->get('customer_info_fields/create', [CustomerInfoFieldController::class, 'create'])->name('customer_info_fields.create');
                Route::middleware('can:customer_info_fields_create')->post('customer_info_fields', [CustomerInfoFieldController::class, 'store'])->name('customer_info_fields.store');
                Route::middleware('can:customer_info_fields_show')->get('customer_info_fields/{id}/edit', [CustomerInfoFieldController::class, 'edit'])->name('customer_info_fields.edit');
                Route::middleware('can:customer_info_fields_edit')->put('customer_info_fields/{id}', [CustomerInfoFieldController::class, 'update'])->name('customer_info_fields.update');
                Route::middleware('can:customer_info_fields_delete')->delete('customer_info_fields/{id}', [CustomerInfoFieldController::class, 'destroy'])->name('customer_info_fields.destroy');

                // Webpages
                Route::middleware('can:webpages_index')->get('webpages', [WebpageController::class, 'index'])->name('webpages.index');
                Route::middleware('can:webpages_create')->get('webpages/create', [WebpageController::class, 'create'])->name('webpages.create');
                Route::middleware('can:webpages_create')->post('webpages', [WebpageController::class, 'store'])->name('webpages.store');
                Route::middleware('can:webpages_show')->get('webpages/{id}/edit', [WebpageController::class, 'edit'])->name('webpages.edit');
                Route::middleware('can:webpages_edit')->put('webpages/{id}', [WebpageController::class, 'update'])->name('webpages.update');
                Route::middleware('can:webpages_delete')->delete('webpages/{id}', [WebpageController::class, 'destroy'])->name('webpages.destroy');

                // design_menu
                Route::middleware('can:design_menu_index')->get('design/menu', [DesignMenuController::class, 'index'])->name('design_menu.index');
                Route::middleware('can:design_menu_edit')->put('design/menu', [DesignMenuController::class, 'update'])->name('design_menu.update');

                // Stores
                Route::middleware('can:stores_index')->get('stores', [StoreController::class, 'index'])->name('stores.index');
                Route::middleware('can:stores_create')->get('stores/create', [StoreController::class, 'create'])->name('stores.create');
                Route::middleware('can:stores_create')->post('stores', [StoreController::class, 'store'])->name('stores.store');
                Route::middleware('can:stores_show')->get('stores/{id}/edit', [StoreController::class, 'edit'])->name('stores.edit');
                Route::middleware('can:stores_edit')->put('stores/{id}', [StoreController::class, 'update'])->name('stores.update');
                Route::middleware('can:stores_delete')->delete('stores/{id}', [StoreController::class, 'destroy'])->name('stores.destroy');
                // Combos
                Route::middleware('can:combos_index')->get('combos', [ComboController::class, 'index'])->name('combos.index');
                Route::middleware('can:combos_create')->get('combos/create', [ComboController::class, 'create'])->name('combos.create');
                Route::middleware('can:combos_create')->post('combos', [ComboController::class, 'store'])->name('combos.store');
                Route::middleware('can:combos_show')->get('combos/{id}/edit', [ComboController::class, 'edit'])->name('combos.edit');
                Route::middleware('can:combos_edit')->put('combos/{id}', [ComboController::class, 'update'])->name('combos.update');
                Route::middleware('can:combos_delete')->delete('combos/{id}', [ComboController::class, 'destroy'])->name('combos.destroy');
                // Coupons
                Route::middleware('can:coupons_index')->get('coupons', [CouponController::class, 'index'])->name('coupons.index');
                Route::middleware('can:coupons_create')->get('coupons/create', [CouponController::class, 'create'])->name('coupons.create');
                Route::middleware('can:coupons_create')->post('coupons', [CouponController::class, 'store'])->name('coupons.store');
                Route::middleware('can:coupons_show')->get('coupons/{id}/edit', [CouponController::class, 'edit'])->name('coupons.edit');
                Route::middleware('can:coupons_edit')->put('coupons/{id}', [CouponController::class, 'update'])->name('coupons.update');
                Route::middleware('can:coupons_delete')->delete('coupons/{id}', [CouponController::class, 'destroy'])->name('coupons.destroy');
                // Issues
                Route::middleware('can:issues_index')->get('issues', [IssueController::class, 'index'])->name('issues.index');
                Route::middleware('can:issues_show')->get('issues/{id}/edit', [IssueController::class, 'show'])->name('issues.show');
                Route::middleware('can:issues_edit')->post('issues/{id}/comments', [IssueController::class, 'commentCreate'])->name('issues.comments.create');
                Route::middleware('can:issues_edit')->put('issues/{id}/close', [IssueController::class, 'close'])->name('issues.comments.close');
                Route::middleware('can:issues_edit')->put('issues/{id}/assign', [IssueController::class, 'assign'])->name('issues.comments.assign');
                // Orders
                Route::middleware('can:orders_index')->get('orders', [OrderController::class, 'index'])->name('orders.index');
                Route::middleware('can:orders_show')->get('orders/{id}', [OrderController::class, 'show'])->name('orders.show');
                Route::middleware('can:orders_edit')->put('orders/{id}/paid', [OrderController::class, 'changePaid'])->name('orders.paid');
                // Promotions
                Route::middleware('can:commissions_index')->get('commissions', [CommissionController::class, 'index'])->name('commissions.index');
                Route::middleware('can:commissions_show')->get('commissions/{id}', [CommissionController::class, 'show'])->name('commissions.show');
                // Plugin orders
                Route::middleware('can:plugin_orders_index')->get('plugin_orders', [PluginOrderController::class, 'index'])->name('plugin_orders.index');
                Route::middleware('can:plugin_orders_show')->get('plugin_orders/{id}', [PluginOrderController::class, 'show'])->name('plugin_orders.show');
                Route::middleware('can:plugin_orders_edit')->put('plugin_orders/{id}/paid', [PluginOrderController::class, 'changePaid'])->name('plugin_orders.paid');
                // 语言管理
                Route::middleware('can:languages_index')->get('languages', [LanguageController::class, 'index'])->name('languages.index');
                Route::middleware('can:languages_create')->post('languages', [LanguageController::class, 'store'])->name('languages.store');
                Route::middleware('can:languages_edit')->put('languages/{id}', [LanguageController::class, 'update'])->name('languages.update');
                Route::middleware('can:languages_delete')->delete('languages/{id}', [LanguageController::class, 'destroy'])->name('languages.destroy');

                // 文章
                Route::get('pages', [PagesController::class, 'index'])->name('pages.index');
                Route::middleware('can:pages_show')->get('pages/names', [PagesController::class, 'getNames'])->name('pages.names');
                Route::middleware('can:pages_create')->get('pages/create', [PagesController::class, 'create'])->name('pages.create');
                Route::middleware('can:pages_show')->get('pages/{page}/edit', [PagesController::class, 'edit'])->name('pages.edit');
                Route::middleware('can:pages_show')->get('pages/{page}/name', [PagesController::class, 'name'])->name('pages.name');
                Route::middleware('can:pages_edit')->post('pages', [PagesController::class, 'store'])->name('pages.store');
                Route::middleware('can:pages_edit')->put('pages/{page}', [PagesController::class, 'update'])->name('pages.update');
                Route::middleware('can:pages_delete')->delete('pages/{page}', [PagesController::class, 'destroy'])->name('pages.destroy');

                // 文章
//                Route::get('abtest', [\Beike\SaasAdmin\Http\Controllers\AbTestController::class, 'index'])->name('admin.abtest.index');

                // 视频
                Route::get('video', [VideoController::class, 'index'])->name('video.index');
                Route::get('video/create', [VideoController::class, 'create'])->name('video.create');
                Route::get('video/{id}/edit', [VideoController::class, 'edit'])->name('video.edit');
                Route::post('video/save', [VideoController::class, 'save'])->name('video.save');
                Route::post('video/update/{id}', [VideoController::class, 'update'])->name('video.update');
                Route::post('video/del/{id}', [VideoController::class, 'del'])->name('video.del');

                Route::get('videoCategories', [VideoCategoryController::class, 'index'])->name('categories.video');
                Route::get('videoCategories/create', [VideoCategoryController::class, 'create'])->name('video_categories.create');
                Route::get('videoCategories/{id}/edit', [VideoCategoryController::class, 'edit'])->name('video_categories.edit');
                Route::get('videoCategories/select', [VideoCategoryController::class, 'select'])->name('video_categories.name');
                Route::post('videoCategories/save', [VideoCategoryController::class, 'save'])->name('video_categories.save');
                Route::post('videoCategories/update/{id}', [VideoCategoryController::class, 'update'])->name('video_categories.update');
                Route::post('videoCategories/del/{id}', [VideoCategoryController::class, 'delete'])->name('video_categories.del');




                // 文章分类
                Route::middleware('can:page_categories_index')->get('page_categories', [PageCategoryController::class, 'index'])->name('page_categories.index');
                Route::middleware('can:page_categories_show')->get('page_categories/autocomplete', [PageCategoryController::class, 'autocomplete'])->name('page_categories.autocomplete');
                Route::middleware('can:page_categories_show')->get('page_categories/create', [PageCategoryController::class, 'create'])->name('page_categories.create');
                Route::middleware('can:page_categories_edit')->get('page_categories/{category}/edit', [PageCategoryController::class, 'edit'])->name('page_categories.edit');
                Route::middleware('can:page_categories_show')->get('page_categories/{page_category}/name', [PageCategoryController::class, 'name'])->name('page_categories.name');
                Route::middleware('can:page_categories_create')->post('page_categories', [PageCategoryController::class, 'store'])->name('page_categories.store');
                Route::middleware('can:page_categories_edit')->put('page_categories/{page_category}', [PageCategoryController::class, 'update'])->name('page_categories.update');
                Route::middleware('can:page_categories_delete')->delete('page_categories/{page_category}', [PageCategoryController::class, 'destroy'])->name('page_categories.destroy');

                Route::get('plugins/store/select', [MarketingController::class, 'select'])->name('plugins.index');
                Route::middleware('can:plugins_index')->get('plugins', [MarketingController::class, 'index'])->name('plugins.index');
                Route::middleware('can:plugins_index')->post('plugins', [MarketingController::class, 'store'])->name('plugins.store');
                Route::middleware('can:plugins_index')->get('plugins/{code}', [MarketingController::class, 'edit'])->name('plugins.edit');
                Route::middleware('can:plugins_index')->post('plugins/{code}', [MarketingController::class, 'update'])->name('plugins.update');

                // 日志
                Route::middleware('can:loggers_index')->get('loggers', [LoggerController::class, 'index'])->name('loggers.index');
                Route::middleware('can:loggers_show')->get('loggers/download', [LoggerController::class, 'download'])->name('loggers.download');
                Route::middleware('can:loggers_show')->get('loggers/info', [LoggerController::class, 'show'])->name('loggers.show');

                // Partner
                Route::middleware('can:partners_index')->get('partners', [PartnerController::class, 'index'])->name('partners.index');
//                Route::middleware('can:partners_create')->get('partners/create', [PartnerController::class, 'create'])->name('partners.create');
                Route::middleware('can:partners_create')->post('partners', [PartnerController::class, 'store'])->name('partners.store');
                Route::middleware('can:partners_show')->get('partners/{id}/edit', [PartnerController::class, 'edit'])->name('partners.edit');
                Route::middleware('can:partners_edit')->post('partners/{id}', [PartnerController::class, 'update'])->name('partners.update');
                Route::post('partners/check/{id}', [PartnerController::class, 'check'])->name('partners.check');
                Route::middleware('can:partners_delete')->post('partners/del/{id}', [PartnerController::class, 'destroy'])->name('partners.destroy');
                Route::get('partners/type/select', [PartnerController::class, 'servers'])->name('partners.type');
                Route::get('partners/server/select', [PartnerController::class, 'type'])->name('partners.server');
                Route::get('partners/country/select', [PartnerController::class, 'country'])->name('partners.country');
                Route::get('partners/zone/{id}', [PartnerController::class, 'zone'])->name('partners.zone');
                Route::get('test/email', [PartnerController::class, 'send'])->name('partners.demo');

                //person
                Route::get('person', [PersonController::class, 'index'])->name('person.index');
                Route::post('person', [PersonController::class, 'store'])->name('person.store');
                Route::get('person/{id}/edit', [PersonController::class, 'edit'])->name('person.edit');
                Route::post('person/{id}', [PersonController::class, 'update'])->name('person.update');
                Route::post('person/check/{id}', [PersonController::class, 'check'])->name('person.check');
                Route::post('person/del/{id}', [PersonController::class, 'destroy'])->name('person.destroy');

                //服务类型
                Route::get('server', [ServerController::class, 'index'])->name('server.index');
                Route::get('server/create', [ServerController::class, 'create'])->name('server.create');
                Route::post('server', [ServerController::class, 'store'])->name('server.store');
                Route::get('server/{id}/edit', [ServerController::class, 'edit'])->name('server.edit');
                Route::post('server/{id}', [ServerController::class, 'update'])->name('server.update');
                Route::post('server/del/{id}', [ServerController::class, 'destroy'])->name('server.destroy');

                // 报表
                Route::middleware('can:reports_order')->get('reports/order', [ReportController::class, 'order'])->name('reports_order');
                Route::middleware('can:reports_customer')->get('reports/customer', [ReportController::class, 'customer'])->name('reports_customer');

                Route::get('contacts', [\Beike\SaasAdmin\Http\Controllers\ContactController::class, 'index'])->name('saas_contacts.index');
                Route::get('contacts/select', [\Beike\SaasAdmin\Http\Controllers\ContactController::class, 'select'])->name('saas_contacts.select');
                Route::get('contacts/edit/{id}', [\Beike\SaasAdmin\Http\Controllers\ContactController::class, 'edit'])->name('saas_contacts.edit');
                Route::get('contacts/del/{id}', [\Beike\SaasAdmin\Http\Controllers\ContactController::class, 'del'])->name('saas_contacts.del');
                Route::post('contacts/save', [\Beike\SaasAdmin\Http\Controllers\ContactController::class, 'save'])->name('saas_contacts.save');




            });

    });
