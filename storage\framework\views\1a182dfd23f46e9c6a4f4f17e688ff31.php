<?php if (! ($breadcrumbs->isEmpty())): ?>
<div class="breadcrumb-wrap">
  <div class="container">
    <nav aria-label="breadcrumb">
      <ol class="breadcrumb">
        <?php $__currentLoopData = $breadcrumbs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $breadcrumb): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
          <?php if(isset($breadcrumb['url']) && $breadcrumb['url']): ?>
          <li class="breadcrumb-item"><a href="<?php echo e($breadcrumb['url']); ?>"><?php echo e($breadcrumb['title']); ?></a></li>
          <?php else: ?>
          <li class="breadcrumb-item active" aria-current="page"><?php echo e($breadcrumb['title']); ?></li>
          <?php endif; ?>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
      </ol>
    </nav>
  </div>
</div>
<?php endif; ?><?php /**PATH D:\shopleadeCont\git\saas\themes\default/components/breadcrumbs.blade.php ENDPATH**/ ?>