
<?php if(!empty($gift) && count($gift) > 0): ?>
<div class="d-lg-flex">
  <span class="title text-muted"><?php echo e(__('Gift::setting.text1')); ?>:</span>
  <div>
    <?php $__currentLoopData = $gift; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div><?php echo e($item['description']['name']); ?></div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
  </div>
</div>
<?php endif; ?>
<?php /**PATH D:\shopleadeCont\git\saas\plugins/Gift/Views/shop/product_gift.blade.php ENDPATH**/ ?>