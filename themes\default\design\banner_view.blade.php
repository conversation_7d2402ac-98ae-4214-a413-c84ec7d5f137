<section class="module-item banner-view-module {{ $design ? 'module-item-design' : '' }}" id="module-{{ $module_id }}"
    style="background-color: {{ $content['style']['background_color'] ?? '#fff' }};">
    @include('design._partial._module_tool')
    <div class="banner-container {{ $content['full'] ? '' : '' }}">
        <a style="display: block;" href="{{ $content['link'] ?? '#' }}">
            <div class="banner-content">
                @if (!empty($content['images']))
                    <div class="banner-images">
@php
                        function renderMedia($mediaUrl, $altText) {
                            $ext = pathinfo($mediaUrl, PATHINFO_EXTENSION);
                            if (in_array($ext, ['mp4', 'mov', 'avi'])) {
                                echo '<video style="height:100%" src="' . image_origin($mediaUrl) . '" autoplay muted loop playsinline></video>';
                            } else {
                                echo '<img src="' . image_origin($mediaUrl) . '" alt="' . $altText . '">';
                            }
                        }
                        @endphp
                        @if (isset($content['images'][0]))
                            <div class="banner-image d-none d-lg-block">
                                @php renderMedia($content['images'][0][app()->getLocale()] ?? '', $content['banner_text'] ?? ''); @endphp
                            </div>
                        @endif
                        @if (isset($content['images'][1]))
                            <div class="banner-image d-lg-none">
                                @php renderMedia($content['images'][1][app()->getLocale()] ?? '', $content['banner_text'] ?? ''); @endphp
                            </div>
                        @endif
                    </div>
                @endif
                <div class="banner-text-overlay">
                    @if (!empty($content['banner_text']))
                        <h1>{{ $content['banner_text'] }}</h1>
                    @endif
                    @if (!empty($content['banner_des']))
                        <h5>{{ $content['banner_des'] }}</h5>
                    @endif
                    @if (!empty($content['button_text']))
                        <span>{{ $content['button_text'] }}</span>
                    @endif
                </div>
            </div>
        </a>
    </div>
</section>
