<template id="address-dialog">
  <div class="address-dialog">
    <el-dialog custom-class="mobileWidth" title="{{ __('address.index') }}" :visible.sync="editShow" @close="closeAddressDialog('addressForm')" :close-on-click-modal="false">
      <el-form ref="addressForm" :rules="rules" label-position="top" :model="form" label-width="100px">
        <div class="d-flex">
          <el-form-item label="{{ __('address.name') }}" class="w-50" prop="name">
            <el-input v-model="form.name" placeholder="{{ __('address.name') }}"></el-input>
          </el-form-item>
          <el-form-item label="{{ __('address.last_name') }}" class="w-50 ms-3" prop="last_name">
            <el-input v-model="form.last_name" placeholder="{{ __('address.last_name') }}"></el-input>
          </el-form-item>
        </div>
        <div class="d-flex">
          @if (!current_customer())
          <el-form-item label="{{ __('common.email') }}" prop="email" v-if="type == 'guest_shipping_address'" class="w-100">
            <el-input v-model="form.email" placeholder="{{ __('common.email') }}"></el-input>
          </el-form-item>
          @else
          <el-form-item label="{{ __('address.address_1') }}"  class="w-100" prop="address_1">
            <el-input v-model="form.address_1" placeholder="{{ __('address.address_1') }}"></el-input>
          </el-form-item>
          @endif
        </div>
        @if (!current_customer())
        <el-form-item label="{{ __('address.address_1') }}" prop="address_1">
          <el-input v-model="form.address_1" placeholder="{{ __('address.address_1') }}"></el-input>
        </el-form-item>
        @endif
        <div class="d-flex">
          <el-form-item label="{{ __('address.address_2') }}" class="w-50">
            <el-input v-model="form.address_2" placeholder="{{ __('address.address_2') }}"></el-input>
          </el-form-item>
          <el-form-item label="{{ __('address.post_code') }}" class="w-50 ms-3">
            <el-input v-model="form.zipcode" placeholder="{{ __('address.post_code') }}"></el-input>
          </el-form-item>
        </div>
        <div class="d-flex dialog-address">
          <el-form-item label="{{ __('address.phone') }}" class="w-50" prop="phone">
              <div style="display: flex;">
                         <div class="calling-code-select el-input__inner" @click.stop="cityKey = true">
             <div class="flex line-center flex-center">
                 <img :src="'/image/flag/' + selectedCallingCode.icon + '.png'"
                     class="img-fluid" style="width: 16px">
                 <span>+ @{{ form.calling_code }} </span>
                 <div class="dropdown-menu calling-code-dropdown"
                     :style="{ 'display': cityKey ? 'block' : 'none' }">
                     <div class="position-relative">
                         <input type="text"
                             placeholder="Search your country and region"
                             name="" class="form-control ps-4"
                             id="calling-code-search">
                     </div>
                     <ul class="code-list">
                         <li v-for="itemCode in callingCodes"
                             @click.stop='(form.calling_code = itemCode.code) && (selectedCallingCode = itemCode) && (cityKey = false)'
                             :data-text="itemCode.region + ' ' + itemCode.code">
                             <span>
                                 <img :src="'/image/flag/' + itemCode.icon + '.png'"
                                     class="img-fluid">
                                 @{{ itemCode.region }}
                             </span>
                             <span>@{{ itemCode.code }}</span>
                         </li>
                     </ul>
                 </div>
             </div>

                                                </div>
                <el-input maxlength="11" v-model="form.phone" type="number" placeholder="{{ __('address.phone') }}"></el-input>
              </div>
          </el-form-item>
          <el-form-item prop="city" label="{{ __('shop/account/addresses.enter_city') }}" required class="w-50 ms-3">
            <el-input v-model="form.city" placeholder="{{ __('shop/account/addresses.enter_city') }}"></el-input>
          </el-form-item>
        </div>
        <div class="d-flex">
          <el-form-item label="{{ __('address.country') }}" required class="w-50">
            <el-select v-model="form.country_id" class="w-100" filterable placeholder="{{ __('address.country_id') }}" @change="countryChange">
              <el-option v-for="item in source.countries" :key="item.id" :label="item.name"
              :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="zone_id" label="{{ __('address.zone') }}" class="w-50 ms-3">
            <el-select v-model="form.zone_id" class="w-100" filterable placeholder="{{ __('address.zone') }}">
              <el-option v-for="item in source.zones" :key="item.id" :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <el-form-item label="" v-if="source.isLogin">
          <span class="me-2">{{ __('address.default') }}</span> <el-switch v-model="form.default"></el-switch>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addressFormSubmit('addressForm')">{{ __('common.save') }}</el-button>
          <el-button @click="closeAddressDialog('addressForm')">{{ __('common.cancel') }}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
   const cityData = [{"icon":"0001","region":"USA\/Canada","code":"1"},{"icon":"0376","region":"Andorra","code":"376"},{"icon":"0971","region":"United Arab Emirates","code":"971"},{"icon":"0093","region":"Afghanistan","code":"93"},{"icon":"1268","region":"Antigua and Barbuda","code":"1268"},{"icon":"1264","region":"Anguilla","code":"1264"},{"icon":"0355","region":"Albania","code":"355"},{"icon":"0374","region":"Armenia","code":"374"},{"icon":"0247","region":"Ascension","code":"247"},{"icon":"0244","region":"Angola","code":"244"},{"icon":"0054","region":"Argentina","code":"54"},{"icon":"0043","region":"Austria","code":"43"},{"icon":"0061","region":"Australia","code":"61"},{"icon":"0994","region":"Azerbaijan","code":"994"},{"icon":"1246","region":"Barbados","code":"1246"},{"icon":"0880","region":"Bangladesh","code":"880"},{"icon":"0032","region":"Belgium","code":"32"},{"icon":"0226","region":"Burkina-faso","code":"226"},{"icon":"0359","region":"Bulgaria","code":"359"},{"icon":"0973","region":"Bahrain","code":"973"},{"icon":"0257","region":"Burundi","code":"257"},{"icon":"0229","region":"Benin","code":"229"},{"icon":"0970","region":"Palestine","code":"970"},{"icon":"1441","region":"Bermuda Is.","code":"1441"},{"icon":"0673","region":"Brunei","code":"673"},{"icon":"0591","region":"Bolivia","code":"591"},{"icon":"0055","region":"Brazil","code":"55"},{"icon":"1242","region":"Bahamas","code":"1242"},{"icon":"0267","region":"Botswana","code":"267"},{"icon":"0375","region":"Belarus","code":"375"},{"icon":"0501","region":"Belize","code":"501"},{"icon":"1345","region":"Cayman Is.","code":"1345"},{"icon":"0236","region":"Central African Republic","code":"236"},{"icon":"0242","region":"Congo","code":"242"},{"icon":"0041","region":"Switzerland","code":"41"},{"icon":"0682","region":"Cook Is.","code":"682"},{"icon":"0056","region":"Chile","code":"56"},{"icon":"0237","region":"Cameroon","code":"237"},{"icon":"0086","region":"China","code":"86"},{"icon":"0057","region":"Colombia","code":"57"},{"icon":"0506","region":"Costa Rica","code":"506"},{"icon":"0420","region":"Czech","code":"420"},{"icon":"0053","region":"Cuba","code":"53"},{"icon":"0357","region":"Cyprus","code":"357"},{"icon":"0420","region":"Czech Republic","code":"420"},{"icon":"0049","region":"Germany","code":"49"},{"icon":"0253","region":"Djibouti","code":"253"},{"icon":"0045","region":"Denmark","code":"45"},{"icon":"0213","region":"Algeria","code":"213"},{"icon":"0593","region":"Ecuador","code":"593"},{"icon":"0372","region":"Estonia","code":"372"},{"icon":"0020","region":"Egypt","code":"20"},{"icon":"0034","region":"Spain","code":"34"},{"icon":"0251","region":"Ethiopia","code":"251"},{"icon":"0358","region":"Finland","code":"358"},{"icon":"0679","region":"Fiji","code":"679"},{"icon":"0033","region":"France","code":"33"},{"icon":"0241","region":"Gabon","code":"241"},{"icon":"0044","region":"United Kiongdom","code":"44"},{"icon":"0995","region":"Georgia","code":"995"},{"icon":"0594","region":"French Guiana","code":"594"},{"icon":"0233","region":"Ghana","code":"233"},{"icon":"0350","region":"Gibraltar","code":"350"},{"icon":"0220","region":"Gambia","code":"220"},{"icon":"0224","region":"Guinea","code":"224"},{"icon":"0030","region":"Greece","code":"30"},{"icon":"0502","region":"Guatemala","code":"502"},{"icon":"1671","region":"Guam","code":"1671"},{"icon":"0592","region":"Guyana","code":"592"},{"icon":"0852","region":"Hongkong","code":"852"},{"icon":"0504","region":"Honduras","code":"504"},{"icon":"0509","region":"Haiti","code":"509"},{"icon":"0036","region":"Hungary","code":"36"},{"icon":"0062","region":"Indonesia","code":"62"},{"icon":"0353","region":"Ireland","code":"353"},{"icon":"0972","region":"Israel","code":"972"},{"icon":"0091","region":"India","code":"91"},{"icon":"0964","region":"Iraq","code":"964"},{"icon":"0098","region":"Iran","code":"98"},{"icon":"0354","region":"Iceland","code":"354"},{"icon":"0039","region":"Italy","code":"39"},{"icon":"0225","region":"Ivory Coast","code":"225"},{"icon":"1876","region":"Jamaica","code":"1876"},{"icon":"0962","region":"Jordan","code":"962"},{"icon":"0081","region":"Japan","code":"81"},{"icon":"0254","region":"Kenya","code":"254"},{"icon":"0855","region":"Kampuchea(Cambodia)","code":"855"},{"icon":"0850","region":"North Korea","code":"850"},{"icon":"0082","region":"Korea","code":"82"},{"icon":"0225","region":"Republic of Ivory Coast","code":"225"},{"icon":"0965","region":"Kuwait","code":"965"},{"icon":"0856","region":"Laos","code":"856"},{"icon":"0961","region":"Lebanon","code":"961"},{"icon":"1758","region":"St.Lucia","code":"1758"},{"icon":"0094","region":"Sri Lanka","code":"94"},{"icon":"0231","region":"Liberia","code":"231"},{"icon":"0266","region":"Lesotho","code":"266"},{"icon":"0370","region":"Lithuania","code":"370"},{"icon":"0352","region":"Luxembourg","code":"352"},{"icon":"0371","region":"Latvia","code":"371"},{"icon":"0218","region":"Libya","code":"218"},{"icon":"0212","region":"Morocco","code":"212"},{"icon":"0377","region":"Monaco","code":"377"},{"icon":"0373","region":"Republic of Moldova","code":"373"},{"icon":"0261","region":"Madagascar","code":"261"},{"icon":"0223","region":"Mali","code":"223"},{"icon":"0095","region":"Burma","code":"95"},{"icon":"0976","region":"Mongolia","code":"976"},{"icon":"0853","region":"Macao","code":"853"},{"icon":"1664","region":"Montserrat Is","code":"1664"},{"icon":"0356","region":"Malta","code":"356"},{"icon":"0596","region":"Martinique","code":"596"},{"icon":"0230","region":"Mauritius","code":"230"},{"icon":"0960","region":"Maldives","code":"960"},{"icon":"0265","region":"Malawi","code":"265"},{"icon":"0052","region":"Mexico","code":"52"},{"icon":"0060","region":"Malaysia","code":"60"},{"icon":"0258","region":"Mozambique","code":"258"},{"icon":"0264","region":"Namibia","code":"264"},{"icon":"0977","region":"Niger","code":"977"},{"icon":"0234","region":"Nigeria","code":"234"},{"icon":"0505","region":"Nicaragua","code":"505"},{"icon":"0031","region":"Netherlands","code":"31"},{"icon":"0047","region":"Norway","code":"47"},{"icon":"0977","region":"Nepal","code":"977"},{"icon":"0599","region":"Netheriands Antilles","code":"599"},{"icon":"0674","region":"Nauru","code":"674"},{"icon":"0064","region":"New Zealand","code":"64"},{"icon":"0968","region":"Oman","code":"968"},{"icon":"0507","region":"Panama","code":"507"},{"icon":"0051","region":"Peru","code":"51"},{"icon":"0689","region":"French Polynesia","code":"689"},{"icon":"0675","region":"Papua New Cuinea","code":"675"},{"icon":"0063","region":"Philippines","code":"63"},{"icon":"0092","region":"Pakistan","code":"92"},{"icon":"0048","region":"Poland","code":"48"},{"icon":"1787","region":"Puerto Rico","code":"1787"},{"icon":"0351","region":"Portugal","code":"351"},{"icon":"0595","region":"Paraguay","code":"595"},{"icon":"0974","region":"Qatar","code":"974"},{"icon":"0262","region":"Reunion","code":"262"},{"icon":"0040","region":"Romania","code":"40"},{"icon":"0007","region":"Russia","code":"7"},{"icon":"0966","region":"Saudi Arabia","code":"966"},{"icon":"0677","region":"Solomon Is","code":"677"},{"icon":"0248","region":"Seychelles","code":"248"},{"icon":"0249","region":"Sudan","code":"249"},{"icon":"0046","region":"Sweden","code":"46"},{"icon":"0065","region":"Singapore","code":"65"},{"icon":"0386","region":"Slovenia","code":"386"},{"icon":"0421","region":"Slovakia","code":"421"},{"icon":"0232","region":"Sierra Leone","code":"232"},{"icon":"0378","region":"San Marino","code":"378"},{"icon":"0684","region":"Samoa Eastern","code":"684"},{"icon":"0685","region":"San Marino","code":"685"},{"icon":"0221","region":"Senegal","code":"221"},{"icon":"0252","region":"Somali","code":"252"},{"icon":"0597","region":"Suriname","code":"597"},{"icon":"0239","region":"Sao Tome and Principe","code":"239"},{"icon":"0503","region":"EI Salvador","code":"503"},{"icon":"0963","region":"Syria","code":"963"},{"icon":"0268","region":"Swaziland","code":"268"},{"icon":"0235","region":"Chad","code":"235"},{"icon":"0228","region":"Togo","code":"228"},{"icon":"0066","region":"Thailand","code":"66"},{"icon":"0992","region":"Tajikstan","code":"992"},{"icon":"0993","region":"Turkmenistan","code":"993"},{"icon":"0216","region":"Tunisia","code":"216"},{"icon":"0676","region":"Tonga","code":"676"},{"icon":"0090","region":"Turkey","code":"90"},{"icon":"0886","region":"Taiwan","code":"886"},{"icon":"0255","region":"Tanzania","code":"255"},{"icon":"0380","region":"Ukraine","code":"380"},{"icon":"0256","region":"Uganda","code":"256"},{"icon":"0598","region":"Uruguay","code":"598"},{"icon":"0233","region":"Uzbekistan","code":"233"},{"icon":"1784","region":"Saint Vincent","code":"1784"},{"icon":"0058","region":"Venezuela","code":"58"},{"icon":"0084","region":"Vietnam","code":"84"},{"icon":"0967","region":"Yemen","code":"967"},{"icon":"0381","region":"Yugoslavia","code":"381"},{"icon":"0027","region":"South Africa","code":"27"},{"icon":"0260","region":"Zambia","code":"260"},{"icon":"0243","region":"Zaire","code":"243"},{"icon":"0263","region":"Zimbabwe","code":"263"}]
  Vue.component('address-dialog', {
  template: '#address-dialog',
  props: {
    value: {
      default: null
    },
  },

  data: function () {
    return {
      editShow: false,
      index: null,
      type: 'shipping_address_id',
      cityKey:false,
      callingCodes : cityData,
      selectedCallingCode : cityData[0],
      
      form: {
        name: '',
        email: '',
        phone: '',
        country_id: @json((int) system_setting('base.country_id')),
        zipcode: '',
        zone_id: @json((int) system_setting('base.zone_id')),
        city: '',
        address_1: '',
        address_2: '',
        default: false,
        calling_code : cityData[0].code,
        last_name : ''
      },

      rules: {
        name: [{
          required: true,
          message: '{{ __('BlackProducts::header.text10') }}',
          trigger: ['change','blur'],
          validator: (rule, value, callback) => {
            if (!/^[a-zA-Z]+$/.test(value)) {
              callback(new Error('{{ __('BlackProducts::header.text10') }}'));
            } else {
              callback();
            }
          }
        }, ],
          last_name: [{
          required: true,
          message: '{{ __('BlackProducts::header.text9') }}',
           trigger: ['change','blur'],
          validator: (rule, value, callback) => {
            if (!/^[a-zA-Z]+$/.test(value)) {
              callback(new Error('{{ __('BlackProducts::header.text9') }}'));
            } else {
              callback();
            }
          }
        }, ],
        email: [{
          required: true,
          type: 'email',
          message: '{{ __('shop/login.enter_email') }}',
          trigger: 'blur'
        }, ],
        address_1: [{
          required: true,
          message: ' {{ __('shop/account/addresses.enter_address') }}',
          trigger: 'blur'
        }, ],
        zone_id: [{
          required: true,
          message: '{{ __('shop/account/addresses.select_province') }}',
          trigger: 'blur'
        }, ],
        city: [{
          required: true,
          message: '{{ __('shop/account/addresses.enter_city') }}',
          trigger: 'blur'
        }, ],
        phone: [{
          required: true,
          message: '{{ __('address.phone') }}',
          trigger: 'blur'
        }, ],
      },

      source: {
        countries: @json($countries ?? []),
        zones: [],
        isLogin: config.isLogin,
      },
    }
  },

  computed: {
  },

  beforeMount() {
    this.countryChange(this.form.country_id);
    const styleDom = document.createElement('style');
    styleDom.innerHTML = `
    .calling-code-select {
                 white-space: nowrap;
                 border: 1px solid;
                 border-right: none;
                 border-color: inherit;
                 padding: 0px 20px 0px 10px;
                 height:40px;
                 width: fit-content;
          }
              .calling-code-dropdown {
              padding: 10px;
             width: 330px;
         }

         .calling-code-dropdown:not(.el) {
             font-size: 13px;
             box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
         }

         .calling-code-dropdown.el .el-dropdown-menu__item {
             padding: 0px;
             font-size: 13px;
             line-height: inherit;
             background-color: transparent !important;
             color: inherit !important;
         }

         .calling-code-dropdown hr {
             opacity: 0.08;
         }

         .calling-code-dropdown .code-list {
             max-height: 200px;
             overflow-y: auto;
             margin-top: 14px;
             padding-left:0;
         }

         .calling-code-dropdown .code-list li {
             padding: 2px 14px;
             cursor: pointer;
             display: flex;
             justify-content: space-between;
             align-items: center;
         }

         .calling-code-dropdown .code-list li:hover {
             background-color: #f5f5f5;
         }

         .calling-code-dropdown .code-list li img {
             width: 18px;
             margin-right: 2px;
         }

         .calling-code-dropdown #calling-code-search {
             background-image: none;
             border-color: inherit;
         }
    `;
    document.head.appendChild(styleDom);
  },
  mounted() {
      $(document).on('input', '#calling-code-search', function() {
      var value = $(this).val().toLowerCase();
      $('.code-list li').each(function() {
          var text = $(this).data('text').toLowerCase();
          if (text.indexOf(value) === -1) {
              $(this).hide();
          } else {
              $(this).show();
          }
      });
  });
  },

  methods: {
    editAddress(addresses, type) {
      this.type = type
      if (addresses) {
        this.form = addresses;
        if(addresses.calling_code){
             this.selectedCallingCode = this.callingCodes.find(item => item.code == addresses.calling_code)
        }
      }

      this.countryChange(this.form.country_id);
      this.editShow = true
    },

    addressFormSubmit(form) {
      this.$refs[form].validate((valid) => {
        if (!valid) {
          this.$message.error('{{ __('shop/checkout.check_form') }}');
          return;
        }

        this.$emit('change', this.form)
        // const type = this.form.id ? 'put' : 'post';

        // const url = `/account/addresses${type == 'put' ? '/' + this.form.id : ''}`;

        // $http[type](url, this.form).then((res) => {
        //   this.$message.success(res.message);
        //   this.$emit('change', res.data)
        //   this.editShow = false
        // })
      });
    },

    closeAddressDialog() {
      this.$refs['addressForm'].resetFields();
      this.editShow = false

      Object.keys(this.form).forEach(key => this.form[key] = '')
      this.form.country_id = @json((int) system_setting('base.country_id'));
      this.form.default = false;
    },

    countryChange(e) {
      const self = this;

      $http.get(`/countries/${e}/zones`, null, {
        hload: true
      }).then((res) => {
        this.source.zones = res.data.zones;

        if (!res.data.zones.some(e => e.id == this.form.zone_id)) {
          this.form.zone_id = '';
        }
      })
    },
  }
});
</script>