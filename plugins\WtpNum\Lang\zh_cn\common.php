<?php
/**
 * common.php
 *
 * @copyright  2024 beikeshop.com - All Rights Reserved
 * @link       https://beikeshop.com
 * <AUTHOR> <<EMAIL>>
 * @created    2024-05-13 16:19:06
 * @modified   2024-05-13 16:19:06
 */

return [
    'merchant_id' => '商户号',
    'md5_key' => 'Md5 Key',
    'api_key' => 'API Key',
    'api' => '环境',
    'api_test' => '沙箱环境',
    'api_live' => '生产环境',
    'log' => '日志',
    'disable' => '启用',
    'enable' => '禁用',
    'paying' => '支付中',

    'card_number' => '卡号',
    'text_expiry' => '有效期',
    'cvv' => 'CVV',
    'expiry_year' => '年',
    'expiry_month' => '月',
    'expiry_year_month' => '月/年',
    'holder_name' => '持卡人姓名',

    'pay_submit_success'       => '支付请求已成功提交，请稍后查看支付结果',
    'paid_success'             => '您已成功支付!',

    'card_image' => '信用卡图片',
    'payment_type' => '支付方式',
    'card' => '信用卡',
    'wdd' => '数字货币(wdd)',
    'giropay' => 'Giropay',
    'bancontact' => 'Bancontact',
    'konbini' => 'Konbini',
    'grabpay' => 'Grabpay',
    'alfamart' => 'Alfamart',
    'safety_pay' => 'Safety Pay',
    'bancomat_pay' => 'Bancomat Pay',

    'card_type' => '卡类型',
    'card_type_visa' => 'Visa',
    'card_type_mastercard' => 'Mastercard',
    'card_type_ae' => 'AE',
    'card_type_dc' => 'Discover',
    'card_type_jcb' => 'Jcb',
    'card_type_dclub' => 'DClub',
];
