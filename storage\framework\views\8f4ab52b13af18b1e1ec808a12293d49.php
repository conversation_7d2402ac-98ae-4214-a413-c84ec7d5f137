
<?php $__env->startSection('body-class', 'page-product'); ?>
<?php $__env->startSection('title', $product['meta_title'] ?: $product['name']); ?>
<?php $__env->startSection('keywords', $product['meta_keywords'] ?: system_setting('base.meta_keyword')); ?>
<?php $__env->startSection('description', $product['meta_description'] ?: system_setting('base.meta_description')); ?>

<?php $__env->startPush('header'); ?>
  <script src="<?php echo e(asset('vendor/vue/2.7/vue' . (!config('app.debug') ? '.min' : '') . '.js')); ?>"></script>
  <script src="<?php echo e(asset('vendor/swiper/swiper-bundle.min.js')); ?>"></script>
  <link rel="stylesheet" href="<?php echo e(asset('vendor/swiper/swiper-bundle.min.css')); ?>">
  <style>
    .product-image .left #swiper .swiper-slide {
      height: auto !important;
      max-height: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .product-image .left #swiper .swiper-slide a {
      display: block;
      width: 100%;
      height: 100%;
    }
    .product-image .left #swiper .swiper-slide img {
      width: 100%;
      height: 100%;
      max-height: 600px !important;
      object-fit: cover;
    }
    
    /* ElevateZoom 样式 */
    .zoomContainer {
      z-index: 999;
    }
    .zoomLens {
      border: 1px solid rgba(255, 255, 255, 0.8) !important;
      background-color: rgba(255, 255, 255, 0.3) !important;
      cursor: crosshair !important;
    }
    .zoomWindow {
      border: 1px solid #ddd !important;
      box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;
      border-radius: 2px !important;
      background-color: #fff !important;
    }
  </style>
   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("prodcut.gift.css",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
  <?php if($product['video'] && strpos($product['video'], '<iframe') === false): ?>
  <script src="<?php echo e(asset('vendor/video/video.min.js')); ?>"></script>
  <link rel="stylesheet" href="<?php echo e(asset('vendor/video/video-js.min.css')); ?>">
  <?php endif; ?>
<?php $__env->stopPush(); ?>

<?php
  $iframeClass = request('iframe') ? 'd-none' : '';
?>

<?php $__env->startSection('content'); ?>
  <?php if(!request('iframe')): ?>
    <?php if (isset($component)) { $__componentOriginalaaf2a10efb487c03115f53565e62c23d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalaaf2a10efb487c03115f53565e62c23d = $attributes; } ?>
<?php $component = Beike\Shop\View\Components\Breadcrumb::resolve(['type' => 'product','value' => $product['id']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('shop-breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Beike\Shop\View\Components\Breadcrumb::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalaaf2a10efb487c03115f53565e62c23d)): ?>
<?php $attributes = $__attributesOriginalaaf2a10efb487c03115f53565e62c23d; ?>
<?php unset($__attributesOriginalaaf2a10efb487c03115f53565e62c23d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalaaf2a10efb487c03115f53565e62c23d)): ?>
<?php $component = $__componentOriginalaaf2a10efb487c03115f53565e62c23d; ?>
<?php unset($__componentOriginalaaf2a10efb487c03115f53565e62c23d); ?>
<?php endif; ?>
  <?php endif; ?>

  <div class="container <?php echo e(request('iframe') ? 'pt-4' : ''); ?>" id="product-app" v-cloak>
    <div class="row mb-md-5 mt-md-0" id="product-top">
      <div class="col-12 col-lg-6 mb-2">
        <div class="product-image">
          <?php if(!is_mobile()): ?>
            <div class="swiper-main-container">
              <?php echo $__env->make('product.product-video', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
              <div class="swiper" id="swiper-main">
                <div class="swiper-wrapper">
                  <div class="swiper-slide" v-for="image, index in images" :key="index">
                    <img :src="image.preview" class="img-fluid">
                  </div>
                </div>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
              </div>
              <div id="magnifier-lens" style="display: none;"></div>
              <div id="magnifier-pane" style="display: none;"></div>
            </div>
            <div class="swiper-thumbs-container mt-2" v-if="images.length > 1">
              <div class="swiper" id="swiper-thumbs">
                <div class="swiper-wrapper">
                  <div class="swiper-slide" v-for="image, index in images" :key="index">
                    <img :src="image.thumb" class="img-fluid" :data-image="image.preview" :data-zoom-image="image.popup" @load="onThumbImageLoad">
                  </div>
                </div>
              </div>
            </div>
          <?php else: ?>
            <?php echo $__env->make('product.product-video', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <div class="swiper" id="swiper-mobile">
              <div class="swiper-wrapper">
                <div class="swiper-slide d-flex align-items-center justify-content-center" v-for="image, index in images" :key="index">
                  <img :src="image.preview" class="img-fluid" :data-image="image.preview" :data-zoom-image="image.popup">
                </div>
              </div>
              <div class="swiper-pagination mobile-pagination"></div>
            </div>
          <?php endif; ?>
        </div>
      </div>

      <div class="col-12 col-lg-6">
        <div class="peoduct-info product-mb-block">
           <?php
                    $__hook_name="product.detail.name";
                    ob_start();
                ?>
          <h1 class="mb-lg-4 mb-2 product-name"><?php echo e($product['name']); ?></h1>
           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
           <?php
                    $__hook_name="product.detail.price";
                    ob_start();
                ?>
          <?php if((system_setting('base.show_price_after_login') and current_customer()) or !system_setting('base.show_price_after_login')): ?>
          <div class="price-wrap d-flex align-items-end">
            <div class="new-price fs-1 lh-1 fw-bold me-2">{{ product.price_format }}</div>
            <div class="old-price text-muted text-decoration-line-through" v-if="product.price != product.origin_price && product.origin_price !== 0">
              {{ product.origin_price_format }}
            </div>
          </div>
          <?php else: ?>
          <div class="product-price">
            <div class="text-dark fs-6"><?php echo e(__('common.before')); ?> <a class="price-new fs-6 login-before-show-price" href="javascript:void(0);"><?php echo e(__('common.login')); ?></a> <?php echo e(__('common.show_price')); ?></div>
          </div>
          <?php endif; ?>

           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.detail.price.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>

           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
          <div class="stock-and-sku mb-lg-4 mb-2">
             <?php
                    $__hook_name="product.detail.quantity";
                    ob_start();
                ?>
            <div class="d-lg-flex">
              <span class="title text-muted"><?php echo e(__('product.quantity')); ?>:</span>
              <span :class="product.quantity > 0 ? 'text-success' : 'text-secondary'">
                <template v-if="product.quantity > 0"><?php echo e(__('shop/products.in_stock')); ?></template>
                <template v-else><?php echo e(__('shop/products.out_stock')); ?></template>
              </span>
            </div>
             <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>

            <?php if($product['brand_id']): ?>
             <?php
                    $__hook_name="product.detail.brand";
                    ob_start();
                ?>
            <div class="d-lg-flex">
              <span class="title text-muted"><?php echo e(__('product.brand')); ?>:</span>
              <a href="<?php echo e(shop_route('brands.show', $product['brand_id'])); ?>"><?php echo e($product['brand_name']); ?></a>
            </div>
             <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
            <?php endif; ?>

             <?php
                    $__hook_name="product.detail.sku";
                    ob_start();
                ?>
            
             <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>

             <?php
                    $__hook_name="product.detail.model";
                    ob_start();
                ?>
            <div class="d-lg-flex" v-if="product.model"><span class="title text-muted"><?php echo e(__('shop/products.model')); ?>:</span> {{ product.model }}</div>
             <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
             <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.gift",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
          </div>
          <?php if(0): ?>
          <div class="rating-wrap d-lg-flex">
            <div class="rating">
              <?php for($i = 0; $i < 5; $i++): ?>
              <i class="iconfont">&#xe628;</i>
              <?php endfor; ?>
            </div>
            <span class="text-muted">132 reviews</span>
          </div>
          <?php endif; ?>
           <?php
                    $__hook_name="product.detail.variables";
                    ob_start();
                ?>
          <div class="variables-wrap mb-md-4" v-if="source.variables.length">
            <div class="variable-group" v-for="variable, variable_index in source.variables" :key="variable_index">
              <p class="mb-2">{{ variable.name }}</p>
              <div class="variable-info">
                <div
                  v-for="value, value_index in variable.values"
                  @click="checkedVariableValue(variable_index, value_index, value)"
                  :key="value_index"
                  data-bs-toggle="tooltip"
                  data-bs-placement="top"
                  :title="value.image ? value.name : ''"
                  :class="[value.selected ? 'selected' : '', value.disabled ? 'disabled' : '', value.image ? 'is-v-image' : '']">
                  <span class="image" v-if="value.image"><img :src="value.image" class="img-fluid"></span>
                  <span v-else>{{ value.name }}</span>
                </div>
              </div>
            </div>
          </div>
           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>

          <div class="product-btns">
            <?php if($product['active']): ?>
              <div class="quantity-btns">
                 <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.detail.buy.before",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
                 <?php
                    $__hook_name="product.detail.quantity.input";
                    ob_start();
                ?>
                <div class="quantity-wrap">
                  <input type="text" class="form-control" :disabled="!product.quantity" onkeyup="this.value=this.value.replace(/\D/g,'')" v-model="quantity" name="quantity">
                  <div class="right">
                    <i class="bi bi-chevron-up"></i>
                    <i class="bi bi-chevron-down"></i>
                  </div>
                </div>
                 <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
                 <?php
                    $__hook_name="product.detail.add_to_cart";
                    ob_start();
                ?>
                <button
                  class="btn btn-outline-dark ms-md-3 add-cart fw-bold"
                  :product-id="product.id"
                  :product-price="product.price"
                  :disabled="!product.quantity"
                  @click="addCart(false, this)"
                  ><i class="bi bi-cart-fill me-1"></i><?php echo e(__('shop/products.add_to_cart')); ?>

                </button>
                 <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
                 <?php
                    $__hook_name="product.detail.buy_now";
                    ob_start();
                ?>
                <button
                  class="btn btn-dark ms-3 btn-buy-now fw-bold"
                  :disabled="!product.quantity"
                  :product-id="product.id"
                  :product-price="product.price"
                  @click="addCart(true, this)"
                  ><i class="bi bi-bag-fill me-1"></i><?php echo e(__('shop/products.buy_now')); ?>

                </button>
                 <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
                 <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.detail.buy.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
              </div>

              <?php if(current_customer() || !request('iframe')): ?>
                 <?php
                    $__hook_name="product.detail.wishlist";
                    ob_start();
                ?>
                <div class="add-wishlist">
                  <button class="btn btn-link ps-md-0 text-secondary" data-in-wishlist="<?php echo e($product['in_wishlist']); ?>" onclick="bk.addWishlist('<?php echo e($product['id']); ?>', this)">
                    <i class="bi bi-heart<?php echo e($product['in_wishlist'] ? '-fill' : ''); ?> me-1"></i> <span><?php echo e(__('shop/products.add_to_favorites')); ?></span>
                  </button>
                </div>
                 <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
              <?php endif; ?>
            <?php else: ?>
              <div class="text-danger"><i class="bi bi-exclamation-circle-fill"></i> <?php echo e(__('product.has_been_inactive')); ?></div>
            <?php endif; ?>
          </div>

           <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.detail.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
        </div>
      </div>
    </div>

    <div class="product-description product-mb-block <?php echo e($iframeClass); ?>">
      <div class="nav nav-tabs nav-overflow justify-content-start justify-content-md-center border-bottom mb-3">
        <a class="nav-link fw-bold active fs-5" data-bs-toggle="tab" href="#product-description">
          <?php echo e(__('shop/products.product_details')); ?>

        </a>
        <?php if($product['attributes']): ?>
        <a class="nav-link fw-bold fs-5" data-bs-toggle="tab" href="#product-attributes">
          <?php echo e(__('admin/attribute.index')); ?>

        </a>
        <?php endif; ?>
         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.tab.after.link",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
      </div>
      <div class="tab-content">
        <div class="tab-pane fade show active" id="product-description" role="tabpanel">
          <?php echo $product['description']; ?>

        </div>
        <div class="tab-pane fade" id="product-attributes" role="tabpanel">
          <table class="table table-bordered attribute-table">
            <?php $__currentLoopData = $product['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <thead class="table-light">
                <tr><td colspan="2"><strong><?php echo e($group['attribute_group_name']); ?></strong></td></tr>
              </thead>
              <tbody>
                <?php $__currentLoopData = $group['attributes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr>
                  <td><?php echo e($item['attribute']); ?></td>
                  <td><?php echo e($item['attribute_value']); ?></td>
                </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </tbody>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </table>
        </div>
         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.tab.after.pane",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
      </div>
    </div>
  </div>

  <?php if($relations && !request('iframe')): ?>
    <div class="relations-wrap mt-2 mt-md-5 product-mb-block">
      <div class="container position-relative">
        <div class="title text-center"><?php echo e(__('admin/product.product_relations')); ?></div>
        <div class="product swiper-style-plus">
          <div class="swiper relations-swiper">
            <div class="swiper-wrapper">
              <?php $__currentLoopData = $relations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <div class="swiper-slide">
                <?php echo $__env->make('shared.product', ['product' => $item], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
              </div>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
          </div>
          <div class="swiper-pagination relations-pagination"></div>
          <div class="swiper-button-prev relations-swiper-prev"></div>
          <div class="swiper-button-next relations-swiper-next"></div>
        </div>
      </div>
    </div>
  <?php endif; ?>

   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("product.detail.footer",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('add-scripts'); ?>
  <script>
    let swiperMobile = null;
    const isIframe = bk.getQueryString('iframe', false);
    const isMobile = <?php echo e(is_mobile() ? 'true' : 'false'); ?>;

    // 手写放大镜功能
    class ImageMagnifier {
      constructor(imgSelector, options = {}) {
     
        this.img = document.querySelector(imgSelector);
   
        
        if (!this.img) {
       
          return;
        }
        
        this.options = {
          lensSize: 100,
          zoomLevel: 2,
          ...options
        };
        
      
        this.init();
      }
      
      init() {
       
        this.createElements();
        this.bindEvents();
     
      }
      
      createElements() {
       
        
        // 创建放大镜容器
        this.container = document.createElement('div');
        this.container.className = 'magnifier-container';
        this.container.style.cssText = `
          position: relative;
          display: inline-block;
          cursor: crosshair;
        `;
        
        // 创建镜头
        this.lens = document.createElement('div');
        this.lens.className = 'magnifier-lens';
        this.lens.style.cssText = `
          position: absolute;
          border: 1px solid rgba(0, 0, 0, 0.3);
          background-color: rgba(255, 255, 255, 0.2);
          width: ${this.options.lensSize}px;
          height: ${this.options.lensSize}px;
          pointer-events: none;
          display: none;
          z-index: 10;
        `;
        
        // 创建放大窗口
        this.zoomWindow = document.createElement('div');
        this.zoomWindow.className = 'magnifier-zoom-window';
        this.zoomWindow.style.cssText = `
          position: absolute;
          width: 400px;
          height: 400px;
          border: 1px solid #e0e0e0;
          background: #fff;
          display: none;
          overflow: hidden;
          z-index: 999;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        `;
        
        // 创建放大图片
        this.zoomImg = document.createElement('img');
        this.zoomImg.style.cssText = `
          position: absolute;
          max-width: none;
          left: 0;
          top: 0;
        `;
        
        // 组装元素
        const parent = this.img.parentNode;
        parent.insertBefore(this.container, this.img);
        this.container.appendChild(this.img);
        this.container.appendChild(this.lens);
        
        // 将放大图片添加到放大窗口
        this.zoomWindow.appendChild(this.zoomImg);
        
        // 将放大窗口添加到图片容器的父级
        const imageContainer = this.container.closest('.col-lg-6') || this.container.parentNode;
        if (imageContainer) {
          imageContainer.style.position = 'relative';
          imageContainer.appendChild(this.zoomWindow);

        } else {
          document.body.appendChild(this.zoomWindow);
       
        }

      }
      
      bindEvents() {

        
        this.container.addEventListener('mouseenter', (e) => {

          if (this.img.complete && this.img.naturalWidth > 0) {
            this.lens.style.display = 'block';
            this.updateZoomWindowPosition();
            this.updateZoomImage();
            this.zoomWindow.style.display = 'block';

          } else {
            console.log('图片未加载完成');
          }
        });
        
        this.container.addEventListener('mouseleave', (e) => {
   
          this.lens.style.display = 'none';
          this.zoomWindow.style.display = 'none';
        });
        
        this.container.addEventListener('mousemove', (e) => {
          if (this.zoomWindow.style.display === 'block') {
            this.updateLensPosition(e);
          }
        });
        

      }
      
      updateLensPosition(e) {
        const rect = this.img.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 计算镜头位置
        let lensX = x - this.options.lensSize / 2;
        let lensY = y - this.options.lensSize / 2;
        
        // 边界检查
        lensX = Math.max(0, Math.min(lensX, this.img.offsetWidth - this.options.lensSize));
        lensY = Math.max(0, Math.min(lensY, this.img.offsetHeight - this.options.lensSize));
        
        this.lens.style.left = lensX + 'px';
        this.lens.style.top = lensY + 'px';
        
        // 更新放大图片位置
        const ratioX = lensX / (this.img.offsetWidth - this.options.lensSize);
        const ratioY = lensY / (this.img.offsetHeight - this.options.lensSize);
        
        const zoomImgX = -ratioX * (this.zoomImg.offsetWidth - this.zoomWindow.offsetWidth);
        const zoomImgY = -ratioY * (this.zoomImg.offsetHeight - this.zoomWindow.offsetHeight);
        
        this.zoomImg.style.left = zoomImgX + 'px';
        this.zoomImg.style.top = zoomImgY + 'px';
      }
      
      updateZoomImage() {
        
        let zoomImageSrc = this.img.src; // 默认使用当前图片
        
        // 获取当前激活的slide索引
        const activeSlideIndex = swiperMain ? swiperMain.activeIndex : 0;
        
        // 直接从Vue的images数组中获取对应的高清图片
        if (window.app && window.app.images && window.app.images[activeSlideIndex]) {
          const currentImage = window.app.images[activeSlideIndex];
          if (currentImage.popup) {
            zoomImageSrc = currentImage.popup;
          } else if (currentImage.preview) {
            zoomImageSrc = currentImage.preview;
          }
        } else {
          console.log('从Vue数据获取图片失败，使用当前图片');
        }
        
        this.zoomImg.src = zoomImageSrc;
        
        // 等待图片加载完成后设置尺寸
        if (this.zoomImg.complete && this.zoomImg.naturalWidth > 0) {
          this.setZoomImageSize();
        } else {
          this.zoomImg.onload = () => {
            this.setZoomImageSize();
          };
        }
      }
      
      setZoomImageSize() {
        // 减小放大倍数到1.5倍，更合适的放大效果
        const zoomWidth = this.zoomImg.naturalWidth * 1.2;
        const zoomHeight = this.zoomImg.naturalHeight * 1.2;
        
        this.zoomImg.style.width = zoomWidth + 'px';
        this.zoomImg.style.height = zoomHeight + 'px';
        
     
      }
      
      updateZoomWindowPosition() {
        const imageContainer = this.container.closest('.col-lg-6') || this.container.parentNode;
        if (imageContainer) {
          // 固定在图片容器的右上角
          this.zoomWindow.style.top = '0px';
          this.zoomWindow.style.left = '100%';
        
        }
      }
      
      destroy() {
      
        if (this.container && this.container.parentNode) {
          this.container.parentNode.insertBefore(this.img, this.container);
          this.container.remove();
        }
        // 移除添加到body的放大窗口
        if (this.zoomWindow && this.zoomWindow.parentNode) {
          this.zoomWindow.remove();
        }
      }
    }

    let currentMagnifier = null;

    function initMagnifier() {
      if (isMobile) return;
      
      // 销毁现有的放大镜
      if (currentMagnifier) {
        currentMagnifier.destroy();
        currentMagnifier = null;
      }
      
      // 等待图片加载完成
      const mainImg = document.querySelector('#swiper-main .swiper-slide-active img');
      if (mainImg && mainImg.complete && mainImg.naturalWidth > 0) {
        currentMagnifier = new ImageMagnifier('#swiper-main .swiper-slide-active img', {
          lensSize: 120,
          zoomLevel: 1.5  // 调整为1.5倍放大
        });
      } else if (mainImg) {
        mainImg.onload = () => {
          currentMagnifier = new ImageMagnifier('#swiper-main .swiper-slide-active img', {
            lensSize: 120,
            zoomLevel: 1.5  // 调整为1.5倍放大
          });
        };
      }
    }

    let app = new Vue({
      el: '#product-app',

      data: {
        selectedVariantsIndex: [], // 选中的变量索引
        images: [],
        product: {
          id: 0,
          images: "",
          model: "",
          origin_price: 0,
          origin_price_format: "",
          position: 0,
          price: 0,
          price_format: "",
          quantity: 0,
          sku: "",
        },
        quantity: 1,
        source: {
          skus: <?php echo json_encode($product['skus'], 15, 512) ?>,
          variables: <?php echo json_encode($product['variables'] ?? [], 15, 512) ?>,
        }
      },

      beforeMount () {
        const skus = JSON.parse(JSON.stringify(this.source.skus));
        const skuDefault = skus.find(e => e.is_default)
        this.selectedVariantsIndex = skuDefault.variants

        // 为 variables 里面每一个 values 的值添加 selected、disabled 字段
        if (this.source.variables.length) {
          this.source.variables.forEach(variable => {
            variable.values.forEach(value => {
              this.$set(value, 'selected', false)
              this.$set(value, 'disabled', false)
            })
          })

          this.checkedVariants()
          this.getSelectedSku(false);
          this.updateSelectedVariantsStatus()
        } else {
          // 如果没有默认的sku，则取第一个sku的第一个值
          this.product = skus[0];
          this.images = <?php echo json_encode($product['images'] ?? [], 15, 512) ?>;
        }
      },

      methods: {
        checkedVariableValue(variable_index, value_index, value) {
          this.source.variables[variable_index].values.forEach((v, i) => {
            v.selected = i == value_index
          })

          this.updateSelectedVariantsIndex();
          this.getSelectedSku();
          this.updateSelectedVariantsStatus()
        },

        // 把对应 selectedVariantsIndex 下标选中 variables -> values 的 selected 字段为 true
        checkedVariants() {
          this.source.variables.forEach((variable, index) => {
            variable.values[this.selectedVariantsIndex[index]].selected = true
          })
        },

        getSelectedSku(reload = true) {
          // 通过 selectedVariantsIndex 的值比对 skus 的 variables
          const sku = this.source.skus.find(sku => sku.variants.toString() == this.selectedVariantsIndex.toString())
          this.images = <?php echo json_encode($product['images'] ?? [], 15, 512) ?>

          if (reload) {
            this.images.unshift(...sku.images)
          }

          this.product = sku;

          if (swiperMobile) {
            swiperMobile.slideTo(0, 0, false)
          }

          this.$nextTick(() => {
            if (swiperMain) {
              swiperMain.update();
              swiperThumbs.update();
              swiperMain.slideTo(0, 0, false);
            }
            
            // 更新放大镜
            setTimeout(initMagnifier, 300);
          });

          closeVideo()
        },

        addCart(isBuyNow = false) {
          bk.addCart({sku_id: this.product.id, quantity: this.quantity, isBuyNow}, null, () => {
            if (isIframe) {
              let index = parent.layer.getFrameIndex(window.name); //当前iframe层的索引
              parent.bk.getCarts();

              setTimeout(() => {
                parent.layer.close(index);

                if (isBuyNow) {
                  parent.location.href = 'checkout'
                } else {
                  parent.$('.btn-right-cart')[0].click()
                }
              }, 400);
            } else {
              if (isBuyNow) {
                location.href = 'checkout'
              }
            }
          });
        },

        updateSelectedVariantsIndex() {
          // 获取选中的 variables 内 value的 下标 index 填充到 selectedVariantsIndex 中
          this.source.variables.forEach((variable, index) => {
            variable.values.forEach((value, value_index) => {
              if (value.selected) {
                this.selectedVariantsIndex[index] = value_index
              }
            })
          })
        },

        updateSelectedVariantsStatus() {
          // skus 里面 quantity 不为 0 的 sku.variants
          const skus = this.source.skus.filter(sku => sku.quantity > 0).map(sku => sku.variants);
          this.source.variables.forEach((variable, index) => {
            variable.values.forEach((value, value_index) => {
              const selectedVariantsIndex = this.selectedVariantsIndex.slice(0);

              selectedVariantsIndex[index] = value_index;
              const selectedSku = skus.find(sku => sku.toString() == selectedVariantsIndex.toString());
              if (selectedSku) {
                value.disabled = false;
              } else {
                value.disabled = true;
              }
            })
          });
        },
        onThumbImageLoad() {
          // 图片加载完成后，确保data属性正确设置
          this.$nextTick(() => {
            if (currentMagnifier) {
              currentMagnifier.updateZoomImage();
            }
          });
        },
      }
    });

    // 将Vue实例暴露到全局
    window.app = app;

    let swiperMain = null;
    let swiperThumbs = null;

    <?php if(!is_mobile()): ?>
      swiperThumbs = new Swiper("#swiper-thumbs", {
        spaceBetween: 10,
        slidesPerView: 6,
        freeMode: true,
        watchSlidesProgress: true,
        slideToClickedSlide: true,
      });
      swiperMain = new Swiper("#swiper-main", {
        spaceBetween: 10,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev",
        },
        thumbs: {
          swiper: swiperThumbs,
        },
        observer: true,
        observeParents: true
      });
      swiperMain.on('slideChange', function () {
        swiperThumbs.slideTo(swiperMain.activeIndex);
        // 切换图片时重新初始化放大镜
        setTimeout(() => {
          initMagnifier();
        }, 200);
      });

      // 缩略图点击时也要更新放大镜
      swiperThumbs.on('slideChange', function () {
        setTimeout(() => {
          if (currentMagnifier) {
            currentMagnifier.updateZoomImage();
          }
        }, 200);
      });
    <?php endif; ?>

    var relationsSwiper = new Swiper ('.relations-swiper', {
      watchSlidesProgress: true,
      autoHeight: true,
      breakpoints:{
        320: {
          slidesPerView: 2,
          spaceBetween: 10,
        },
        768: {
          slidesPerView: 4,
          spaceBetween: 30,
        },
      },
      spaceBetween: 30,
      // 如果需要前进后退按钮
      navigation: {
        nextEl: '.relations-swiper-next',
        prevEl: '.relations-swiper-prev',
      },

      // 如果需要分页器
      pagination: {
        el: '.relations-pagination',
        clickable: true,
      },
    })

    <?php if(is_mobile()): ?>
      swiperMobile = new Swiper("#swiper-mobile", {
        slidesPerView: 1,
        pagination: {
          el: ".mobile-pagination",
        },
        observer: true,
        observeParents: true
      });
    <?php endif; ?>

    // 页面加载完成后初始化放大镜
    $(document).ready(function () {
      setTimeout(initMagnifier, 1000);
    });

    const selectedVariantsIndex = app.selectedVariantsIndex;
    const variables = app.source.variables;

    const selectedVariants = variables.map((variable, index) => {
      return variable.values[selectedVariantsIndex[index]]
    });
  </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\shopleadeCont\git\saas\themes\default/product/product.blade.php ENDPATH**/ ?>