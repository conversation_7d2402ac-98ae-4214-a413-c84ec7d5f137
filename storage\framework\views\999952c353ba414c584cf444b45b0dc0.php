<?php $__env->startPush('header'); ?>
<script src="<?php echo e(plugin_asset('ProductSpecial', 'public/count-down/jquery.countdown.min.js')); ?>"></script>
<script src="<?php echo e(plugin_asset('ProductSpecial', 'public/count-down/moment.min.js')); ?>"></script>
<?php $__env->stopPush(); ?>


<div class="promotion-countdown-wrap d-flex flex-wrap align-items-center justify-content-between mb-3 p-3 mt-lg-n3" v-if="product.promotion_countdown">
  <div class="d-flex align-items-center lh-1">
    <div class="fs-5"><?php echo e(__('ProductSpecial::common.title')); ?>:</div>
    <div class="fs-1 ms-2 fw-bold text-primary">{{ product.promotion_price_format }}</div>
  </div>
  <div class="d-flex align-items-center mt-2 mt-md-0 lh-1">
    <div class="fs-5"><?php echo e(__('ProductSpecial::common.date_end')); ?>:</div>
    <ul class="product-count-down list-unstyled d-flex ms-2 align-items-center mb-0" :data-countdown="product.promotion_countdown"></ul>
  </div>
</div>

<div class="promotion-text-skus mb-3 d-flex d-none">
  <span class="special-product-title"><?php echo e(__('ProductSpecial::common.special_product')); ?>:</span>
  <div class="promotion-text-info"></div>
</div>

<?php $__env->startPush('add-scripts'); ?>
<script>
  // 获取当前时间的时间戳 秒数，存入 localStorage
  let initTime = Math.round(new Date().getTime() / 1000);
  localStorage.setItem('initTime', initTime);

  $(function() {
    countdownFormat($('[data-countdown]').data('countdown'));
  });

  setTimeout(() => {
    app.$watch('product', function (val) {
      if ( val.promotion_countdown ) {
        countdownFormat(val.promotion_countdown);
      }
    });

    let promotion_text_skus = ''; // 那些组合有特价，比如 S+绿色

    let promotionSkus = []

    app.source.skus.forEach((sku, index) => {
      if ( sku.promotion_countdown ) {
        promotionSkus.push(sku.variants);
      }
    });

    if (app.source.skus.length != promotionSkus.length && promotionSkus.length > 0) {
      // promotionSkus 值为 [0,1] [1,0]
      promotionSkus.forEach((variant, index) => {
        let variantText = [];
        // app.source.variables 值为 [{ name: '尺码', values: [{name:'S'},{name:'M'}]}, { name: '颜色', values: [{name:'红色'},{name:'绿色'}]}]
        // 需要找出 app.source.variables 中的 [0,1] [1,0] 对应的值
        variant.forEach((item, index) => {
          variantText.push(app.source.variables[index].values[item].name);
        });
        // promotion_text_skus += variantText.join('/') + '<br>';
        promotion_text_skus += '<span class="text-sku bg-light me-2 px-1 px-2">' + variantText.join('/') + '</span>';
      });

      $('.promotion-text-skus').removeClass('d-none').find('.promotion-text-info').html(promotion_text_skus);
    }
  }, 0);

  function countdownFormat(countdown) {
    const nowTime = Math.round(new Date().getTime() / 1000);
    const timeDifference = nowTime - initTime * 1
    countdown = countdown - timeDifference;
    let end = moment().add(countdown, 'seconds').toDate();
    $('[data-countdown]').countdown(end, function(event) {
      let _itemEnd = event.strftime('%D,%H,%M,%S'), dateHtml = '';
      if ( _itemEnd == '00,00,00,00' ) {
        layer.alert('<?php echo e(__('ProductSpecial::common.special_end')); ?>', {
          title: '<?php echo e(__('common.text_hint')); ?>',
          btn: ['<?php echo e(__('common.confirm')); ?>']
        }, function(index){
          layer.close(index);
          window.location.reload();
        });
      }

      if ( parseInt(event.strftime('%D')) ) dateHtml = '<li>%D</li>:';

      $('[data-countdown]').html(event.strftime(dateHtml + '<li>%H</li>:<li>%M</li>:<li>%S</li>'));
    });
  }
</script>
<style>
  .promotion-countdown-wrap {
    border-top: 2px solid #fd560f;
    background-color: #fff3ef;
  }

  .layui-layer-btn .layui-layer-btn0 {
    background-color: #212529;
    border-color: #212529;
  }

  .product-count-down li {
    font-size: 15px;
    font-weight: bold;
    background-color: #fd560f;
    margin: 0 2px;
    color: #fff;
    padding: 2px 4px;
    border-radius: 2px;
  }

  .promotion-text-info span {
    border: 1px solid #ddd;
  }

  .special-product-title {
    font-size: 14px;
    color: red;
  }

  @media (min-width: 768px) {
    .special-product-title {
      width: 80px;
    }
  }
</style>
<?php $__env->stopPush(); ?><?php /**PATH D:\shopleadeCont\git\saas\plugins/ProductSpecial/Views/shop/product_detail_price_after.blade.php ENDPATH**/ ?>