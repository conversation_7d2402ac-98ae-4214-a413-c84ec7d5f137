<section class="module-item {{ $design ? 'module-item-design' : ''}}" id="module-{{ $module_id }}">
  @include('design._partial._module_tool')

  <div class="module-info mb-3 mb-md-5 module-icon-wrap">
    @if ($content['title'])
    <div class="module-title">{{ $content['title'] }}</div>
    @endif
    <div class="container">
      <div class="icon-container">
        <div class="row">
          @foreach ($content['images'] as $image)
          <div class="col-12 col-md-6 col-xl-3">
            <a href="{{ $image['url'] ?? ($image['link'] ?: 'javascript:void(0)') }}" class="text-decoration-none d-flex mb-3 mb-xl-0">
              <div class="image-item mb-3 me-2">
                <img src="{{ $image['image'] }}" class="img-fluid">
              </div>
              <div>
                @if ($image['text'])
                <p class="text-dark mb-2 fs-5">{{ $image['text'] }}</p>
                @endif
                @if ($image['sub_text'])
                <p class="text-secondary mb-0">{{ $image['sub_text'] }}</p>
                @endif
              </div>
            </a>
          </div>
          @endforeach
        </div>
      </div>
    </div>
  </div>
</section>