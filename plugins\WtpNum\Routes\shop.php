<?php
/**
 * wtp.php
 *
 * @copyright  2024 beikeshop.com - All Rights Reserved
 * @link       https://beikeshop.com
 * <AUTHOR> <<EMAIL>>
 * @created    2024-05-13 18:33:13
 * @modified   2024-05-13 18:33:13
 */

use Beike\Shop\Http\Controllers\CheckoutController;
use Illuminate\Support\Facades\Route;

Route::name('plugin.wtp.')
    ->group(function () {
        Route::post('/wtp_num/{id}', [\Plugin\WtpNum\Controllers\WtpController::class, 'pay'])->name('pay_num');
        Route::post('callback/wtp_num', [\Plugin\WtpNum\Controllers\WtpController::class, 'notify'])->name('notify_num');
        Route::get('callback/wtp_num', [\Plugin\WtpNum\Controllers\WtpController::class, 'notify'])->name('notify_get_num');
    });
