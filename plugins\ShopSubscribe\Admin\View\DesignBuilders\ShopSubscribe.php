<?php

namespace Plugin\ShopSubscribe\Admin\View\DesignBuilders;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ShopSubscribe extends Component
{
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return View
     */
    public function render(): View
    {
        $data['register'] = [
            'code'      => 'shop_subscribe',
            'sort'      => 0,
            'name'      => trans('ShopSubscribe::common.name'),
            'icon'      => 'https://oss.shopleade.com/saas/module/shop_subscribe_icon.png',
            'view_path' => 'ShopSubscribe::shop/design_module_shop_subscribe',
        ];

        return view('ShopSubscribe::admin/design_module_shop_subscribe', $data);
    }
}
