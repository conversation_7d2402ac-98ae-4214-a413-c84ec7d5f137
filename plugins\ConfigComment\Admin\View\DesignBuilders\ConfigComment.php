<?php

namespace Plugin\ConfigComment\Admin\View\DesignBuilders;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ConfigComment extends Component
{
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return View
     */
    public function render(): View
    {
        $data['register'] = [
            'code'      => 'config_comment',
            'sort'      => 0,
            'name'      => trans('ConfigComment::common.name'),
            'icon'      => 'https://oss.shopleade.com/saas/module/config_comment_icon.png',
            'view_path' => 'ConfigComment::shop/design_module_config_comment',
        ];

        return view('ConfigComment::admin/design_module_config_comment', $data);
    }
}
