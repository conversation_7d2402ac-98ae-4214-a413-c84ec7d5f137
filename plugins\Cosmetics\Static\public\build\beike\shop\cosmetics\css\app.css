@charset "UTF-8";
/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:45:17
 */
/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:55:31
 */
@font-face {
  font-family: "iconfont";
  src: url("/fonts/iconfont/iconfont.woff") format("woff"), url("/fonts/iconfont/iconfont.ttf") format("truetype"); /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
}
.iconfont {
  font-family: "iconfont";
  font-size: 1rem;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-09 19:16:39
 * @LastEditTime  2022-09-16 20:55:07
 */
[v-cloak] {
  display: none;
}

body[class^=page-account-] {
  background-color: #F7F8FA;
}
body[class^=page-account-] .breadcrumb-wrap {
  background-color: transparent;
  margin-bottom: 0;
}

@font-face {
  font-family: "Poppins";
  src: url("/fonts/poppins/Poppins-Regular.ttf") format("truetype");
  font-style: normal;
  font-weight: 500;
  font-weight: normal;
  font-display: swap;
}
@font-face {
  font-family: "Poppins";
  src: url("/fonts/poppins/Poppins-Medium.ttf") format("truetype");
  font-style: normal;
  font-display: swap;
  font-weight: 600;
}
@font-face {
  font-family: "Poppins";
  src: url("/fonts/poppins/Poppins-SemiBold.ttf") format("truetype");
  font-style: normal;
  font-display: swap;
  font-weight: 700;
}
h1, h2, h3, h4, h5, h6, b, strong {
  font-weight: 700;
  color: #282828;
}

.min-h1 {
  min-height: 100px;
}

.min-h2 {
  min-height: 200px;
}

.min-h3 {
  min-height: 300px;
}

.min-h4 {
  min-height: 400px;
}

.min-h5 {
  min-height: 500px;
}

.min-h6 {
  min-height: 600px;
}

.wp-100 {
  width: 100px;
}

.wp-200 {
  width: 200px;
}

.wp-300 {
  width: 300px;
}

.wp-400 {
  width: 400px;
}

.wp-500 {
  width: 500px;
}

.wp-600 {
  width: 600px;
}

.wp-700 {
  width: 700px;
}

.wp-800 {
  width: 800px;
}

.wp-900 {
  width: 900px;
}

.wp-1000 {
  width: 1000px;
}

.wp-100- {
  width: calc(100% - 100px);
}

.wp-200- {
  width: calc(100% - 200px);
}

.wp-300- {
  width: calc(100% - 300px);
}

.wp-400- {
  width: calc(100% - 400px);
}

.wp-500- {
  width: calc(100% - 500px);
}

.wp-600- {
  width: calc(100% - 600px);
}

.wp-700- {
  width: calc(100% - 700px);
}

.wp-800- {
  width: calc(100% - 800px);
}

.wp-900- {
  width: calc(100% - 900px);
}

.wp-1000- {
  width: calc(100% - 1000px);
}

.h-min-100 {
  min-height: 100px;
}

.h-min-200 {
  min-height: 200px;
}

.h-min-300 {
  min-height: 300px;
}

.h-min-400 {
  min-height: 400px;
}

.h-min-500 {
  min-height: 500px;
}

.h-min-600 {
  min-height: 600px;
}

.h-min-700 {
  min-height: 700px;
}

.h-min-800 {
  min-height: 800px;
}

.h-min-900 {
  min-height: 900px;
}

.h-min-1000 {
  min-height: 1000px;
}

.w-min-100 {
  min-width: 100px;
}

.w-min-200 {
  min-width: 200px;
}

.w-min-300 {
  min-width: 300px;
}

.w-min-400 {
  min-width: 400px;
}

.w-min-500 {
  min-width: 500px;
}

.w-min-600 {
  min-width: 600px;
}

.w-min-700 {
  min-width: 700px;
}

.w-min-800 {
  min-width: 800px;
}

.w-min-900 {
  min-width: 900px;
}

.w-min-1000 {
  min-width: 1000px;
}

.w-max-100 {
  max-width: 100px;
}

.w-max-200 {
  max-width: 200px;
}

.w-max-300 {
  max-width: 300px;
}

.w-max-400 {
  max-width: 400px;
}

.w-max-500 {
  max-width: 500px;
}

.w-max-600 {
  max-width: 600px;
}

.w-max-700 {
  max-width: 700px;
}

.w-max-800 {
  max-width: 800px;
}

.w-max-900 {
  max-width: 900px;
}

.w-max-1000 {
  max-width: 1000px;
}

.hp-100 {
  height: 100px;
}

.hp-200 {
  height: 200px;
}

.hp-300 {
  height: 300px;
}

.hp-400 {
  height: 400px;
}

.hp-500 {
  height: 500px;
}

.hp-600 {
  height: 600px;
}

.hp-700 {
  height: 700px;
}

.hp-800 {
  height: 800px;
}

.hp-900 {
  height: 900px;
}

.hp-1000 {
  height: 1000px;
}

.wh-10 {
  height: 10px;
  width: 10px;
  flex: 0 0 10px;
}

.wh-20 {
  height: 20px;
  width: 20px;
  flex: 0 0 20px;
}

.wh-30 {
  height: 30px;
  width: 30px;
  flex: 0 0 30px;
}

.wh-40 {
  height: 40px;
  width: 40px;
  flex: 0 0 40px;
}

.wh-50 {
  height: 50px;
  width: 50px;
  flex: 0 0 50px;
}

.wh-60 {
  height: 60px;
  width: 60px;
  flex: 0 0 60px;
}

.wh-70 {
  height: 70px;
  width: 70px;
  flex: 0 0 70px;
}

.wh-80 {
  height: 80px;
  width: 80px;
  flex: 0 0 80px;
}

.wh-90 {
  height: 90px;
  width: 90px;
  flex: 0 0 90px;
}

.wh-100 {
  height: 100px;
  width: 100px;
  flex: 0 0 100px;
}

img {
  max-width: 100%;
  height: auto;
}

.col-form-label.required::before {
  content: "*";
  color: #f56c6c;
  font-size: 12px;
  font-weight: bold;
  margin-right: 5px;
}

.login-pop-box {
  overflow: inherit;
}
@media (max-width: 768px) {
  .login-pop-box {
    width: 90% !important;
    height: 80% !important;
  }
}

.module-title {
  font-size: 1.5rem;
  text-align: center;
  font-weight: bold;
  position: relative;
  padding-bottom: 10px;
  color: #333;
  margin-bottom: 2rem;
}
@media (max-width: 992px) {
  .module-title {
    font-size: 1rem;
  }
}
.module-title:after {
  position: absolute;
  bottom: 0;
  transform: translateX(-50%);
  left: 50%;
  content: "";
  width: 60px;
  height: 2px;
  background: #9cb067;
}

.tinymce-format-p p {
  margin-bottom: 0.5rem;
}

.steps-wrap {
  display: flex;
  justify-content: space-around;
  position: relative;
}
.steps-wrap:before {
  content: "";
  position: absolute;
  top: 14px;
  left: 0;
  width: 100%;
  border-bottom: 3px solid #D7D7D7;
}
.steps-wrap > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.steps-wrap > div.active .number {
  background-color: #3C3D41;
  border-color: #3C3D41;
  color: #eee;
}
.steps-wrap > div.active .title {
  color: #111;
}
.steps-wrap > div .number-wrap {
  padding: 0 4px;
  margin-bottom: 0.5rem;
}
.steps-wrap > div .number {
  border: 2px solid #ddd;
  width: 30px;
  height: 30px;
  background-color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.steps-wrap > div .title {
  color: #848484;
}

.quantity-wrap {
  width: 80px;
  height: 37px;
  display: flex;
  align-content: space-between;
  border: 1px solid #ced4da;
}
.quantity-wrap input {
  border: none;
  padding: 0.5rem;
}
.quantity-wrap > .right {
  display: flex;
  flex-direction: column;
  border-left: 1px solid #ced4da;
}
.quantity-wrap > .right i {
  flex: 1;
  width: 20px;
  height: 17px;
  text-align: center;
  cursor: pointer;
  background-color: #fff;
}
.quantity-wrap > .right i:last-of-type {
  border-top: 1px solid #ced4da;
}
.quantity-wrap > .right i:hover {
  background-color: #eee;
}

.fixed-top-line-fixed {
  position: fixed;
  top: 0;
}

@media (max-width: 768px) {
  .account-sides-wrap {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    cursor: pointer;
    z-index: 9999;
    background: rgba(0, 0, 0, 0.4);
    visibility: hidden;
    transition: all 0.3s ease-out;
  }
  .account-sides-wrap.active {
    visibility: visible;
    opacity: 1;
  }
  .account-sides-wrap.active .account-sides-info {
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .account-sides-info {
    position: fixed;
    z-index: 1046;
    width: 80%;
    top: 0;
    left: 0;
    height: 100%;
    transform: translateX(-100%);
    background-color: #fff;
    transition: all 0.3s ease-out;
  }
}
.account-sides-info .mb-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  border-bottom: 1px solid #eee;
}
.account-sides-info .head {
  display: flex;
  align-items: center;
  padding: 2rem 1rem;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  border-top-left-radius: 0.275rem;
  border-top-right-radius: 0.275rem;
}
@media (min-width: 768px) {
  .account-sides-info .head {
    flex-direction: column;
  }
}
@media (max-width: 768px) {
  .account-sides-info .head {
    padding: 1rem;
    border-bottom: 1px solid #eee;
  }
}
.account-sides-info .head .portrait {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  box-shadow: 0px 3px 6px 0px rgba(0, 0, 0, 0.14);
  border: 2px solid #FFFFFF;
  overflow: hidden;
}
@media (min-width: 768px) {
  .account-sides-info .head .portrait {
    margin-bottom: 1rem;
  }
}
@media (max-width: 768px) {
  .account-sides-info .head .portrait {
    width: 60px;
    height: 60px;
    margin-right: 14px;
  }
}
.account-sides-info .head .account-name {
  font-weight: bold;
  font-size: 1rem;
  margin-bottom: 0.4rem;
}
.account-sides-info .head .account-email {
  color: #666666;
}
.account-sides-info .account-links > a {
  color: #4B566B;
  border: none;
  padding: 0.8rem 1rem;
  border-bottom: 1px solid #EEEEEE;
  transition: all 0.2s ease-in-out;
  text-decoration: none !important;
}
.account-sides-info .account-links > a:last-of-type {
  border-bottom: none;
}
.account-sides-info .account-links > a:hover {
  background-color: #E9ECEF;
}
.account-sides-info .account-links > a.active {
  background-color: #E9ECEF;
  color: #4B566B;
}
.account-sides-info .account-links > a .badge {
  color: #fff;
}

.text-size-min {
  font-size: 12px;
}

.text-truncate-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

@media (max-width: 992px) {
  .breadcrumb-filter {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f6f6f6;
    padding-right: 10px;
  }
  .breadcrumb-filter .mb-filter {
    width: 20px;
    font-size: 18px;
    text-align: center;
  }
  .breadcrumb-filter i {
    line-height: 1;
  }
}
@media (min-width: 992px) {
  .breadcrumb-filter .mb-filter {
    display: none;
  }
}
@media (max-width: 992px) {
  .breadcrumb-filter {
    margin-bottom: 10px;
  }
  .breadcrumb-filter .breadcrumb-wrap {
    margin-bottom: 0;
  }
}

.breadcrumb-wrap {
  background-color: #f6f6f6;
  margin-bottom: 20px;
}
@media (max-width: 768px) {
  .breadcrumb-wrap {
    margin-bottom: 10px;
  }
}
@media (max-width: 768px) {
  .breadcrumb-wrap .breadcrumb {
    padding-top: 0.4rem;
    padding-bottom: 0.4rem;
  }
}

@media (max-width: 768px) {
  .breadcrumb {
    padding-top: 0.8rem;
    padding-bottom: 0.8rem;
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis.line-2 {
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.text-ellipsis.line-3 {
  white-space: normal;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.mobile-paginator {
  display: flex;
  justify-content: center;
  align-items: center;
}
.mobile-paginator .input-group {
  max-width: 60px;
  margin: 0 10px;
}
.mobile-paginator .input-group .input-group-text {
  padding: 0.46rem 0.56rem;
}
.mobile-paginator .input-group #mb-page-input {
  padding: 0.46rem 0.26rem;
  text-align: center;
}
.mobile-paginator .btn {
  border: 1px solid var(--bs-border-color);
}
.mobile-paginator .btn:active {
  background-color: #eee;
}

.address-dialog .el-form-item__label {
  line-height: initial;
}

.iframe-modules-sortable-ghost {
  background-color: #f7f7f7;
  border: 1px dashed #9cb067;
  border-radius: 4px;
  padding: 10px;
  font-size: 20px;
  margin-bottom: 10px;
  text-align: center;
}
.iframe-modules-sortable-ghost .icon {
  display: none;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-03 22:32:29
 * @LastEditTime  2022-09-16 20:55:12
 */
header {
  background-color: #fff;
  border-top: 2px solid #9cb067;
}
header .top-wrap .dropdown:hover,
header .header-content .dropdown:hover {
  background-color: #fff;
}
header .top-wrap .dropdown:hover .dropdown-menu,
header .header-content .dropdown:hover .dropdown-menu {
  margin: 0;
  display: block;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
  border: none;
}
header .top-wrap .dropdown:hover .dropdown-menu.dropdown-menu-end,
header .header-content .dropdown:hover .dropdown-menu.dropdown-menu-end {
  right: 0;
}
header .header-content {
  position: relative;
  padding: 20px 0 15px;
}
header .header-content .logo img {
  max-width: 170px;
  max-height: 50px;
}
header .header-content .left-lm {
  width: 70%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
header .header-content .right-btn {
  width: 30%;
  display: flex;
  justify-content: flex-end;
}
header .header-content .right-btn .nav-link {
  color: #333;
  padding-right: 0.7rem;
  padding-left: 0.7rem;
  position: relative;
}
header .header-content .right-btn .nav-link i {
  font-size: 1.3rem;
}
header .cart-badge-quantity {
  position: absolute;
  left: 25px;
  top: -3px;
  text-align: center;
  font-size: 12px;
  display: none;
  width: 23px;
  zoom: 0.9;
  height: 23px;
  line-height: 24px;
  background-color: #9cb067;
  color: #fff;
  border-radius: 50%;
}
header .menu-box.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 99999;
  padding: 0.3rem 0;
  transition: all 0.3s ease-in-out;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}
header .menu-box.fixed .menu-wrap {
  border-top: none;
}
header .menu-wrap {
  border-top: 1px solid #eee;
}
header .menu-wrap .container {
  max-width: 1140px;
}
@media (min-width: 1200px) {
  header .menu-wrap .navbar-nav .dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%);
  }
  header .menu-wrap .navbar-nav .dropdown > .dropdown-menu {
    left: 50%;
    transform: translate(-50%, 0.5rem);
    transition: all 0.2s ease-in-out;
    transition-property: visibility, transform, opacity;
    visibility: hidden;
    opacity: 0;
    display: block;
    transform-origin: top center;
    border-color: rgba(0, 0, 0, 0.064);
    box-shadow: 0 0.5rem 2rem 0.125rem rgba(140, 152, 164, 0.286);
  }
}
header .menu-wrap > .navbar-nav > .nav-item {
  background-color: transparent;
}
header .menu-wrap > .navbar-nav > .nav-item:hover > .nav-link {
  color: #9cb067;
}
header .menu-wrap > .navbar-nav > .nav-item > .nav-link {
  font-size: 14px;
  padding: 0.8rem 1.2rem;
  position: relative;
}
header .menu-wrap > .navbar-nav > .nav-item > .nav-link .badge {
  position: absolute;
  bottom: 80%;
  padding: 2px 4px;
  font-weight: 400;
  border-radius: 2px;
  zoom: 0.8;
  left: calc(50% - 0px);
  margin-left: 0px;
}
header .menu-wrap > .navbar-nav > .nav-item > .nav-link .badge::before {
  content: "";
  position: absolute;
  top: 100%;
  left: 10px;
  border: 4px solid;
  border-color: inherit;
  border-right-color: rgba(0, 0, 0, 0) !important;
  border-bottom-color: rgba(0, 0, 0, 0) !important;
  border-right-width: 7px;
  border-left-width: 0;
}
header .menu-wrap > .navbar-nav .group-name {
  font-size: 15px;
}
header .menu-wrap > .navbar-nav .ul-children a {
  color: #7a7a7a;
}
header .menu-wrap > .navbar-nav .ul-children a:hover {
  color: #9cb067;
}
header .header-mobile {
  border-bottom: 1px solid #eee;
}
header .header-mobile.fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  box-shadow: 0 8px 14px 0 rgba(0, 0, 0, 0.1);
  background: #fff;
  border-color: transparent;
}
header .header-mobile .mobile-content {
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
header .header-mobile .mobile-content > div {
  width: 33.33%;
}
header .header-mobile .mobile-content > div.center a {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
}
header .header-mobile .mobile-content > div.center a img {
  max-height: 100%;
}
header .header-mobile .mobile-content .left {
  display: flex;
  align-items: center;
}
header .header-mobile .mobile-content .left > div {
  cursor: pointer;
}
header .header-mobile .mobile-content .left > div > i {
  font-size: 1.5rem;
  line-height: 1;
}
header .header-mobile .mobile-content .left .mobile-open-search {
  margin-left: 12px;
}
header .header-mobile .mobile-content .left .mobile-open-search > i {
  font-size: 1.1rem;
}
header .header-mobile .mobile-content .right {
  display: flex;
  justify-content: flex-end;
}
header .header-mobile .mobile-content .right .m-cart .cart-badge-quantity {
  left: 11px;
  top: -9px;
  width: 20px;
  height: 20px;
  line-height: 20px;
}
header .header-mobile .mobile-content .right .mb-account-icon span {
  margin-right: -2px;
  display: inline-block;
  vertical-align: 0.255em;
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
header .header-mobile .mobile-content .right .nav-link {
  padding: 0;
}
header .header-mobile .mobile-content .right .nav-link i {
  font-size: 1.2rem;
}

#offcanvas-search-top {
  height: 100px;
  justify-content: center;
}
#offcanvas-search-top .offcanvas-header {
  width: 100%;
}
#offcanvas-search-top input:focus {
  box-shadow: none;
}
#offcanvas-search-top .btn-close {
  padding: 1rem;
  opacity: 1;
}
#offcanvas-search-top .btn-close:hover {
  background-color: #eee;
}

#offcanvas-right-cart {
  z-index: 999999;
}
#offcanvas-right-cart .select-wrap {
  margin-right: 10px;
  cursor: pointer;
}
#offcanvas-right-cart .select-wrap i {
  font-size: 20px;
  color: #aaa;
}
#offcanvas-right-cart .select-wrap i.bi-check-circle-fill {
  color: #9cb067;
}
#offcanvas-right-cart .offcanvas-right-products .product-list {
  padding: 1rem 0;
  border-top: 1px solid #eee;
}
#offcanvas-right-cart .offcanvas-right-products .product-list .left {
  width: 80px;
  flex: 0 0 80px;
  height: 80px;
  border: 1px solid #eee;
  margin-right: 10px;
}
#offcanvas-right-cart .offcanvas-right-products .product-list .left img {
  max-height: 90px;
}
#offcanvas-right-cart .offcanvas-right-products .product-list .right .price input {
  margin-left: 10px;
  width: 50px;
  height: 24px;
}
#offcanvas-right-cart .offcanvas-right-products .product-list .right .offcanvas-products-delete {
  cursor: pointer;
  color: #999;
}
#offcanvas-mobile-menu {
  width: 80%;
}
#offcanvas-mobile-menu .offcanvas-header {
  padding: 10px 20px 10px 10px;
}
#offcanvas-mobile-menu .mobile-menu-wrap {
  padding: 0;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion {
  border-top: 1px solid #e5e5e5;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item {
  border-bottom: 1px solid #e5e5e5;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text > a {
  flex: 1;
  height: 44px;
  padding-left: 10px;
  display: flex;
  align-items: center;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text > a .badge {
  position: relative;
  margin-left: 13px;
  font-weight: 400;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text > a .badge::before {
  content: "";
  position: absolute;
  top: 50%;
  right: 100%;
  transform: translate(0, -50%);
  border: 4px solid;
  border-right-width: 7px;
  border: 5px solid rgba(0, 0, 0, 0);
  border-right-color: inherit;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text > span {
  width: 44px;
  height: 44px;
  display: flex;
  border-left: 1px solid #e5e5e5;
  align-items: center;
  justify-content: center;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text > span:active {
  background-color: #eee;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text > span[aria-expanded=true] {
  background-color: #eee;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item .nav-item-text > span[aria-expanded=true] i {
  transform: rotate(180deg);
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item > .accordion-collapse {
  padding: 0 10px;
  border-top: 1px solid #e5e5e5;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item > .accordion-collapse .children-group .children-title {
  height: 44px;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item > .accordion-collapse .children-group .children-title span {
  margin-right: -10px;
  width: 44px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item > .accordion-collapse .children-group .children-title span:active {
  background-color: #eee;
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item > .accordion-collapse .children-group .children-title span[aria-expanded=true] i::before {
  content: "\f63b";
}
#offcanvas-mobile-menu .mobile-menu-wrap #menu-accordion .accordion-item > .accordion-collapse .children-group .nav a {
  color: #777;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-03 22:32:29
 * @LastEditTime  2022-09-16 20:48:00
 */
footer {
  background: #f6f6f8;
  margin-top: 1rem;
  position: relative;
  overflow: hidden;
}
footer .footer-wrapper {
  position: relative;
  z-index: 1;
}
@media (min-width: 768px) {
  footer {
    margin-top: 5rem;
  }
}
footer .footer-active {
  outline: 2px dashed #4bb1f0 !important;
}
footer .services-wrap {
  padding: 2.2rem 0;
  border-bottom: 1px solid #e7e7e7;
}
@media (max-width: 768px) {
  footer .services-wrap {
    padding: 0.5rem 0;
  }
}
footer .services-wrap .service-item {
  display: flex;
  align-items: center;
}
footer .services-wrap .service-item .icon {
  width: 38px;
  flex: 0 0 38px;
  margin-right: 14px;
}
@media (max-width: 768px) {
  footer .services-wrap .service-item .icon {
    display: none;
  }
}
footer .services-wrap .service-item p {
  margin-bottom: 0;
}
footer .services-wrap .service-item .title {
  margin-bottom: 4px;
  font-weight: bold;
  font-size: 0.9rem;
  color: #333;
}
footer .services-wrap .service-item .sub-title {
  font-size: 0.7rem;
  color: #8D94A0;
}
@media (min-width: 768px) {
  footer .footer-content {
    padding: 3rem 0;
  }
}
@media (max-width: 768px) {
  footer .footer-content > .row {
    margin: 0;
  }
  footer .footer-content > .row > .col-12 {
    padding-left: 0;
    padding-right: 0;
  }
}
footer .footer-content a {
  color: #666;
}
footer .footer-content a:hover {
  color: #9cb067;
}
footer .footer-content .logo {
  max-width: 240px;
  margin-bottom: 10px;
}
footer .footer-content .text {
  color: #010000;
}
footer .footer-content h6 {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (min-width: 768px) {
  footer .footer-content h6 {
    margin-bottom: 16px;
  }
}
@media (max-width: 768px) {
  footer .footer-content h6 {
    margin-bottom: 0;
    padding: 12px 0;
  }
}
footer .footer-content h6 .icon-open {
  display: none;
  height: 20px;
  width: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  font-size: 18px;
}
@media (max-width: 768px) {
  footer .footer-content h6 .icon-open {
    display: block;
  }
}
footer .footer-content .social-network {
  margin-top: 10px;
  display: flex;
}
footer .footer-content .social-network > a {
  margin-right: 10px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
footer .footer-content .social-network > a:hover {
  transform: translateY(-5px);
}
@media (min-width: 768px) {
  footer .footer-content .intro-title {
    display: none;
  }
}
@media (max-width: 768px) {
  footer .footer-content .footer-link-wrap {
    border-bottom: 1px solid #eee;
  }
}
footer .footer-content .footer-link-wrap.active .icon-open i:before {
  content: "\f63b";
}
footer .footer-content .footer-link-wrap.active ul.list-unstyled, footer .footer-content .footer-link-wrap.active .intro-wrap {
  max-height: 1666px;
}
footer .footer-content .footer-link-wrap ul.list-unstyled, footer .footer-content .footer-link-wrap .intro-wrap {
  margin-bottom: 0;
}
@media (max-width: 768px) {
  footer .footer-content .footer-link-wrap ul.list-unstyled, footer .footer-content .footer-link-wrap .intro-wrap {
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: all 0.35s;
  }
}
footer .footer-bottom {
  display: flex;
  align-items: center;
  background: #EFEFF4;
  color: #666;
}
@media (min-width: 768px) {
  footer .footer-bottom {
    min-height: 60px;
  }
}
@media (max-width: 768px) {
  footer .footer-bottom {
    padding: 10px 0;
  }
  footer .footer-bottom .row.align-items-center, footer .footer-bottom .d-flex {
    justify-content: center;
  }
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-03 22:32:29
 * @LastEditTime  2022-09-16 20:56:27
 */
.product-list-wrap .col-12:not(:last-of-type) .product-wrap:hover {
  box-shadow: none;
}
.product-list-wrap .col-12:not(:last-of-type) .product-wrap:hover .image {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
.product-list-wrap .col-12:not(:last-of-type) .product-wrap:hover .button-wrap {
  bottom: 10px;
  opacity: 1;
}
.product-list-wrap .col-12:not(:last-of-type) .product-wrap:hover .button-wrap button:hover {
  background-color: #9cb067;
  border-color: #9cb067;
  color: #111 !important;
}
.product-list-wrap .col-12:not(:last-of-type) .product-wrap:hover .button-wrap button:hover i {
  color: #111;
}

.row[class*=g-] .product-wrap {
  margin-bottom: 0;
}

.product-wrap {
  margin-bottom: 20px;
  transition: all 0.3s ease-in-out;
  background-color: #fff;
  overflow: hidden;
}
.product-wrap:hover .image {
  border-color: #9cb067;
}
.product-wrap .product-bottom-info {
  transition: all ease-out 0.3s;
}
.product-wrap.list {
  display: flex;
  padding-bottom: 0;
}
.product-wrap.list .image {
  width: 200px;
  margin-bottom: 0;
  transition: all 0.3s ease-in-out;
}
.product-wrap.list .product-name {
  height: auto;
  margin-bottom: 20px;
}
.product-wrap.list .product-bottom-info {
  padding-top: 10px;
  padding-left: 20px;
  flex: 1;
  text-align: left;
}
.product-wrap.list .product-bottom-info .product-name {
  font-size: 14px;
}
.product-wrap .image {
  margin-bottom: 10px;
  position: relative;
  border-bottom: 2px solid #495057;
}
.product-wrap .image .image-old {
  opacity: 1;
  transition: all ease-out 0.3s;
}
.product-wrap .image .button-wrap {
  width: 100%;
  position: absolute;
  z-index: 40;
  bottom: -30px;
  opacity: 0;
  transition: all 0.3s ease-out;
  display: flex;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
  border-top: 1px solid #eee;
}
.product-wrap .image .button-wrap button {
  padding-left: 0;
  padding-right: 0;
  border-radius: 0;
  width: 50%;
  font-size: 18px;
  padding-top: 3px;
  padding-bottom: 3px;
  font-weight: bold;
  border-right: 1px solid #e6e6e6;
}
.product-wrap .image .button-wrap button:first-of-type {
  font-size: 21px;
}
.product-wrap .image .button-wrap button:last-of-type {
  border-right: 0;
}
.product-wrap .image .button-wrap button:hover {
  background-color: #9cb067;
  border-color: #9cb067;
  color: #fff;
}
@media (min-width: 768px) {
  .product-wrap:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }
  .product-wrap:hover .product-bottom-info {
    padding: 0 10px;
  }
  .product-wrap:hover .button-wrap {
    bottom: 0;
    opacity: 1;
  }
}
.product-wrap .product-name {
  height: 39px;
  margin-bottom: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  color: #666;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.product-wrap .product-price .price-new {
  color: #111;
  font-size: 1.1rem;
}
.product-wrap .product-price .price-old {
  color: #aaa;
  margin-left: 4px;
  text-decoration: line-through;
}

@media (max-width: 768px) {
  .product-grid:nth-child(2n+1) {
    padding-right: 5px;
  }
  .product-grid:nth-child(2n+2) {
    padding-left: 5px;
  }
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:47:45
 */
.page-categories-home, .page-pages {
  background-color: #f6f6f6;
}
.page-categories-home .post-item, .page-pages .post-item {
  display: flex;
}
.page-categories-home .post-item .image, .page-pages .post-item .image {
  flex: 0 0 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  border: 1px solid #eee;
}
@media (max-width: 768px) {
  .page-categories-home .post-item .image, .page-pages .post-item .image {
    margin-right: 10px;
    flex: 0 0 100px;
  }
}
@media (max-width: 768px) {
  .page-categories-home .post-item .text-summary, .page-pages .post-item .text-summary {
    display: none;
  }
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-09 19:16:39
 * @LastEditTime  2022-09-16 20:55:37
 */
@media (max-width: 768px) {
  body.page-login, body.page-forgotten {
    overflow-x: hidden;
  }
}
body.page-login .el-form-item__error--inline, body.page-forgotten .el-form-item__error--inline {
  margin-left: 0;
}
body.page-login .forgotten-link, body.page-forgotten .forgotten-link {
  display: block;
  margin-top: -14px;
}
body.page-login .el-form-item, body.page-forgotten .el-form-item {
  margin-bottom: 18px;
}
body.page-login .el-form-item .el-form-item__content, body.page-forgotten .el-form-item .el-form-item__content {
  line-height: 1;
}
body.page-login .login-item-header, body.page-forgotten .login-item-header {
  background: #f8f9fa;
  border-bottom: none;
  padding: 1.2rem 1.5rem;
}
body.page-login .login-item-header h6, body.page-forgotten .login-item-header h6 {
  font-weight: bold;
  font-size: 1rem;
}
body.page-login .vr-wrap, body.page-forgotten .vr-wrap {
  margin: 0 80px;
}
@media (min-width: 768px) {
  body.page-login .login-wrap, body.page-forgotten .login-wrap {
    display: flex;
    justify-content: center;
  }
}
body.page-login .login-wrap .card, body.page-forgotten .login-wrap .card {
  border: none;
}
@media (min-width: 768px) {
  body.page-login .login-wrap .card, body.page-forgotten .login-wrap .card {
    width: 340px;
  }
}
body.page-login .form-iframe, body.page-forgotten .form-iframe {
  margin-bottom: 30px;
}
@media (max-width: 768px) {
  body.page-login .form-iframe, body.page-forgotten .form-iframe {
    padding: 0;
    margin-top: 0 !important;
  }
}
body.page-login .form-iframe .vr-wrap, body.page-forgotten .form-iframe .vr-wrap {
  margin: 0 60px;
}
body.page-login .form-iframe .card, body.page-forgotten .form-iframe .card {
  border: none;
}
body.page-login .social-wrap .title, body.page-forgotten .social-wrap .title {
  position: relative;
  text-align: center;
  color: #999;
}
body.page-login .social-wrap .title::before, body.page-forgotten .social-wrap .title::before {
  content: "";
  position: absolute;
  width: 100%;
  left: 0;
  height: 1px;
  top: 47%;
  background: #e5e5e5;
}
body.page-login .social-wrap .title span, body.page-forgotten .social-wrap .title span {
  background-color: #fff;
  position: relative;
  padding: 0 5px;
  z-index: 1;
}
body.page-login .social-wrap .btn, body.page-forgotten .social-wrap .btn {
  color: #666;
}
body.page-login .social-wrap .btn img, body.page-forgotten .social-wrap .btn img {
  position: absolute;
  left: 40px;
}
body.page-login .social-wrap .btn:hover, body.page-forgotten .social-wrap .btn:hover {
  background-color: #eee;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-01 16:23:34
 * @LastEditTime  2022-09-16 20:57:19
 */
body.page-account {
  background-color: #F7F8FA;
}
body.page-account .account-card {
  border: none;
}
body.page-account .account-card .card-items > a {
  width: 25%;
  color: #444444;
  text-decoration: none !important;
}
body.page-account .account-card .card-items > a i {
  font-size: 2rem;
}
body.page-account .account-card .card-items > a span {
  display: flex;
}
@media (min-width: 992px) {
  body.page-account .account-card .card-body {
    min-height: 509px;
  }
}
body.page-account .account-card .order-wrap {
  background-color: #f6f8f9;
}
@media (min-width: 768px) {
  body.page-account .account-card .order-wrap {
    padding: 2rem 1rem;
  }
}
body.page-account .account-card .order-wrap .icon i {
  font-size: 4.5rem;
  color: #777;
}
body.page-account .account-card .order-wrap .text {
  font-size: 1rem;
}

@media (max-width: 768px) {
  .account-sides-info {
    margin-bottom: 14px;
  }
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-24 17:24:33
 * @LastEditTime  2022-09-16 20:56:21
 */
@media (max-width: 768px) {
  body.page-product {
    background-color: #f2f2f3;
    padding-bottom: 64px;
  }
  body.page-product .breadcrumb-wrap {
    display: none;
  }
}
@media (max-width: 768px) {
  body.page-product #product-app.container {
    padding: 0;
    overflow-x: hidden;
  }
}
@media (max-width: 768px) {
  body.page-product .product-mb-block {
    background-color: #fff;
    margin-bottom: 10px;
    padding: 10px;
  }
}
body.page-product #product-description img {
  max-width: 100%;
  height: auto;
}
body.page-product .product-image {
  position: relative;
}
body.page-product .product-image #swiper {
  height: 250px;
}
@media (min-width: 480px) {
  body.page-product .product-image #swiper {
    height: 400px;
  }
}
@media (min-width: 768px) {
  body.page-product .product-image #swiper {
    height: 500px;
  }
}
body.page-product .product-image #swiper:hover .swiper-pager > div {
  background-color: rgba(255, 255, 255, 0.548);
  opacity: 1;
}
@media (max-width: 768px) {
  body.page-product .product-image #swiper:hover .swiper-pager > div {
    display: none;
  }
}
body.page-product .product-image #swiper:hover .swiper-pager > div:hover {
  background-color: rgb(255, 255, 255);
}
body.page-product .product-image #swiper-mobile {
  width: 100%;
  border-right: 1px solid #eee;
}
body.page-product .product-image #swiper-mobile .swiper-pagination {
  --swiper-theme-color: #ff6600; /* 设置Swiper风格 */
  --swiper-navigation-color: #ff6600; /* 单独设置按钮颜色 */
  --swiper-navigation-size: 30px; /* 设置按钮大小 */
}
body.page-product .product-image .swiper-main-container {
  position: relative;
  border: 1px solid #eee;
}
body.page-product .product-image .swiper-main-container #magnifier-lens,
body.page-product .product-image .swiper-main-container #magnifier-pane {
  display: none !important;
}
body.page-product .product-image .swiper-main-container .zoomContainer .zoomLens {
  border: 1px solid rgba(255, 255, 255, 0.8) !important;
  background-color: rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) !important;
}
body.page-product .product-image .swiper-main-container .zoomContainer .zoomWindow {
  border: 1px solid #ddd !important;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;
  border-radius: 2px !important;
  background-color: #fff !important;
}
body.page-product .product-image .swiper-main-container .swiper {
  position: relative;
  z-index: 1;
}
body.page-product .product-image .swiper-main-container #swiper-main .swiper-slide {
  display: flex;
  align-items: center;
  justify-content: center;
}
body.page-product .product-image .swiper-main-container #swiper-main .swiper-slide img {
  max-width: 100%;
  height: auto;
  display: block;
}
body.page-product .product-image .swiper-main-container .swiper-button-next, body.page-product .product-image .swiper-main-container .swiper-button-prev {
  color: #333;
  background-color: rgba(255, 255, 255, 0.8);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  z-index: 10;
}
body.page-product .product-image .swiper-main-container .swiper-button-next::after, body.page-product .product-image .swiper-main-container .swiper-button-prev::after {
  font-size: 1.2rem;
}
body.page-product .product-image .swiper-main-container:hover .swiper-button-next, body.page-product .product-image .swiper-main-container:hover .swiper-button-prev {
  opacity: 1;
}
body.page-product .product-image .swiper-thumbs-container #swiper-thumbs .swiper-slide {
  cursor: pointer;
  border: 1px solid #eee;
  opacity: 0.6;
  transition: opacity 0.3s;
}
body.page-product .product-image .swiper-thumbs-container #swiper-thumbs .swiper-slide.swiper-slide-thumb-active {
  opacity: 1;
  border: 2px solid #000;
}
body.page-product .product-image #product-video {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  display: none;
}
body.page-product .product-image .open-video {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transform: translateX(-50%);
  left: 50%;
  z-index: 99;
  line-height: 1;
  cursor: pointer;
}
body.page-product .product-image .open-video:hover i {
  color: #fff;
  background-color: rgba(0, 0, 0, 0.648);
}
body.page-product .product-image .open-video i {
  font-size: 4rem;
  line-height: 1;
  border-radius: 50%;
  font-weight: 400;
  display: inline-block;
  color: rgba(255, 255, 255, 0.948);
  background-color: rgba(0, 0, 0, 0.348);
}
@media (max-width: 768px) {
  body.page-product .product-image .open-video i {
    font-size: 3rem;
  }
}
body.page-product .product-image .close-video {
  position: absolute;
  top: 6px;
  right: 10px;
  z-index: 9999;
  color: #aaa;
  font-size: 30px;
  cursor: pointer;
}
body.page-product .product-image .close-video:hover {
  color: #fff;
}
@media (max-width: 768px) {
  body.page-product .stock-and-sku {
    background: #fafafa;
    padding: 8px;
    line-height: 1.6;
  }
}
@media (min-width: 768px) {
  body.page-product .stock-and-sku > div {
    font-size: 14px;
    margin-bottom: 10px;
  }
}
@media (max-width: 768px) {
  body.page-product .stock-and-sku > div {
    display: inline-block;
    margin-right: 10px;
  }
}
@media (min-width: 768px) {
  body.page-product .stock-and-sku > div .title {
    width: 80px;
  }
}
body.page-product .variables-wrap .variable-group {
  margin-bottom: 10px;
}
body.page-product .variables-wrap .variable-group:last-of-type {
  margin-bottom: 0;
}
body.page-product .variables-wrap .variable-info > div {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  border: 1px solid #ddd;
  margin-left: 0;
  min-width: 3rem;
  cursor: pointer;
  text-align: center;
  font-weight: bold;
  flex-direction: column;
  border-radius: 4px;
  transition: all 0.1s ease-in-out;
}
body.page-product .variables-wrap .variable-info > div:hover, body.page-product .variables-wrap .variable-info > div.selected {
  border-color: #222;
}
body.page-product .variables-wrap .variable-info > div:not(.is-v-image) {
  padding: 0.4rem 0.5rem;
}
body.page-product .variables-wrap .variable-info > div > span.image {
  width: 50px;
}
body.page-product .variables-wrap .variable-info > div:not(.selected).disabled {
  border: 1px dashed #2e2929;
  color: #999;
  font-weight: initial;
}
@media (max-width: 768px) {
  body.page-product .product-btns {
    z-index: 100;
    max-height: 102px;
    display: flex;
    align-items: center;
    left: 0;
    right: 0;
    width: 100%;
    background: #fff;
    position: fixed;
    bottom: 0;
    box-shadow: 0 -8px 12px 0 rgba(0, 0, 0, 0.1019607843);
    flex-wrap: wrap;
    padding: 10px 10px calc(10px + env(safe-area-inset-bottom));
  }
  body.page-product .product-btns .quantity-btns {
    flex: 1;
    display: flex;
  }
  body.page-product .product-btns .btn-buy-now {
    display: none;
  }
  body.page-product .product-btns .add-cart {
    flex: 1;
    margin: 0 10px;
    background-color: #212529;
    color: #fff;
  }
  body.page-product .product-btns .add-wishlist .btn {
    padding: 0;
    color: #333 !important;
  }
  body.page-product .product-btns .add-wishlist span {
    display: none;
  }
  body.page-product .product-btns .add-wishlist i {
    font-size: 20px;
  }
}
body.page-product .peoduct-info .product-name {
  font-size: 1.7rem;
  line-height: 1.3;
  font-weight: 600;
}
@media (max-width: 768px) {
  body.page-product .peoduct-info .product-name {
    font-size: 1rem;
    font-weight: normal;
  }
}
body.page-product .peoduct-info .rating-wrap {
  margin-bottom: 2rem;
}
body.page-product .peoduct-info .rating-wrap .rating {
  margin-right: 0.5rem;
}
body.page-product .peoduct-info .rating-wrap .rating i {
  color: #9cb067;
}
body.page-product .peoduct-info .price-wrap {
  margin-bottom: 2.4rem;
}
@media (max-width: 768px) {
  body.page-product .peoduct-info .price-wrap {
    margin-bottom: 1rem;
  }
}
body.page-product .peoduct-info .quantity-btns .btn-buy-now {
  background-color: #9cb067;
  border-color: #9cb067;
}
@media (min-width: 768px) {
  body.page-product .peoduct-info .quantity-btns {
    display: flex;
  }
}
body.page-product .peoduct-info .quantity-btns .quantity-input {
  max-width: 5rem;
  text-align: center;
}
body.page-product .peoduct-info .quantity-btns .quantity-wrap {
  height: 43px;
}
body.page-product .product-description .nav-tabs .nav-link {
  border: none;
}
body.page-product .product-description .nav-tabs .nav-link.active {
  position: relative;
  background-color: transparent;
  color: #9cb067;
}
body.page-product .product-description .nav-tabs .nav-link.active:before {
  border-top: 1px solid #9cb067;
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}
@media (min-width: 768px) {
  body.page-product .attribute-table tr td:first-of-type {
    width: 20%;
  }
}
@media (max-width: 768px) {
  body.page-product .attribute-table tr td:first-of-type {
    width: 40%;
  }
}
@media (max-width: 768px) {
  body.page-product .relations-wrap .container {
    padding: 0 0 10px;
  }
}
body.page-product .relations-wrap .title {
  font-size: 20px;
  margin-bottom: 22px;
}
@media (max-width: 768px) {
  body.page-product .relations-wrap .title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;
  }
}
body.page-product .relations-wrap .swiper-pagination {
  bottom: -10px;
}
body.page-product .relations-wrap .swiper-pagination .swiper-pagination-bullet {
  height: 3px;
  border-radius: 0;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-15 17:35:29
 * @LastEditTime  2022-09-16 20:47:16
 */
body.page-checkout, body.page-cart {
  background-color: #f6f8fa;
}
@media (max-width: 768px) {
  body.page-checkout, body.page-cart {
    padding-bottom: 64px;
  }
}
@media (min-width: 992px) {
  body.page-checkout .left-column, body.page-cart .left-column {
    width: 70%;
  }
}
@media (min-width: 992px) {
  body.page-checkout .right-column, body.page-cart .right-column {
    width: 30%;
  }
}
@media (max-width: 768px) {
  body.page-checkout .right-column, body.page-cart .right-column {
    margin-top: 1.4rem;
  }
}

body.page-cart .cart-products-wrap .table tbody {
  border-top: none;
}
body.page-cart .cart-products-wrap .table tbody td {
  vertical-align: middle;
  border-color: #f0f2f4;
}
body.page-cart .cart-products-wrap .table thead {
  background-color: #F8F9FA;
}
body.page-cart .cart-products-wrap .table thead th {
  border-bottom: none;
  padding: 0.7rem 0.5rem;
  box-shadow: none;
  white-space: nowrap;
}
body.page-cart .cart-products-wrap .table .p-image input {
  flex: 0 0 1;
}
body.page-cart .total-wrap.total-wrap-fixed {
  position: fixed;
  top: 0;
  right: 0;
}
body.page-cart .total-wrap .card-header {
  padding-top: 1rem;
  border-bottom: none;
  background-color: transparent;
}
body.page-cart .total-wrap .list-group li {
  background-color: transparent;
  padding: 0.7rem 0;
  border-color: #EEEEEE;
}
body.page-cart .total-wrap .list-group li:not(.d-grid) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
body.page-cart .total-wrap .list-group li .total-price {
  color: #222222;
  font-size: 1.2rem;
  font-weight: bold;
}
body.page-cart .mb-product-wrap {
  margin-left: -12px;
  margin-right: -12px;
}
body.page-cart .mb-product-wrap .mb-product-list {
  background-color: #fff;
  margin-bottom: 10px;
  padding: 10px;
  display: flex;
}
body.page-cart .mb-product-wrap .mb-product-list .quantity-wrap-line {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
body.page-cart .mb-product-wrap .mb-product-list .quantity-wrap-line input {
  width: 38px;
  border-radius: 0;
}
body.page-cart .mb-product-wrap .mb-product-list .quantity-wrap-line input, body.page-cart .mb-product-wrap .mb-product-list .quantity-wrap-line i {
  height: 24px;
  line-height: 24px;
  padding: 0;
  text-align: center;
  display: block;
}
body.page-cart .mb-product-wrap .mb-product-list .quantity-wrap-line i {
  width: 24px;
  border: 1px solid #dee2e6;
  display: inline-block;
  cursor: pointer;
}
body.page-cart .mb-product-wrap .mb-product-list .quantity-wrap-line .bi-chevron-up {
  transform: rotate(-270deg);
  margin-left: -1px;
}
body.page-cart .mb-product-wrap .mb-product-list .quantity-wrap-line .bi-chevron-down {
  transform: rotate(90deg);
  margin-right: -1px;
}
body.page-cart .cart-mb-total {
  position: fixed;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  background: #fff;
  z-index: 100;
  padding: 8px 12px;
  bottom: 0;
  justify-content: space-between;
  box-shadow: 0 -8px 12px 0 rgba(0, 0, 0, 0.1019607843);
  transition: transform 0.3s ease-in-out;
  padding-bottom: calc(8px + env(safe-area-inset-bottom));
}
body.page-cart .cart-mb-total .right {
  flex: 1;
  text-align: right;
}
body.page-cart .cart-mb-total .right .total-price {
  font-size: 0.9rem;
}
body.page-cart .cart-mb-total .btn-checkout {
  width: 50%;
  margin-left: 4px;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-01 16:23:34
 * @LastEditTime  2022-09-16 20:56:12
 */
body.page-checkout {
  background-color: #f6f8fa;
}
@media (max-width: 992px) {
  body.page-checkout .submit-checkout-wrap {
    position: fixed;
    left: 0;
    right: 0;
    align-items: center;
    background: #fff;
    z-index: 100;
    padding: 8px 12px;
    bottom: 0;
    display: flex !important;
    justify-content: space-between;
    box-shadow: 0 -8px 12px 0 rgba(0, 0, 0, 0.1019607843);
    transition: transform 0.3s ease-in-out;
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
  }
  body.page-checkout .submit-checkout-wrap #submit-checkout {
    width: 50%;
  }
  body.page-checkout .submit-checkout-wrap .text-total {
    font-size: 0.9rem;
  }
}
body.page-checkout .radio-line-wrap .radio-line-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 20px;
  border: 1px solid transparent;
}
@media (max-width: 768px) {
  body.page-checkout .radio-line-wrap .radio-line-item {
    padding: 10px;
  }
}
body.page-checkout .radio-line-wrap .radio-line-item:not(:last-of-type) {
  margin-bottom: 1rem;
}
body.page-checkout .radio-line-wrap .radio-line-item:hover {
  border-color: #e5e5e5;
}
body.page-checkout .radio-line-wrap .radio-line-item:active {
  background-color: #f7f7f7;
}
body.page-checkout .radio-line-wrap .radio-line-item.active {
  border-color: #e5e5e5;
}
body.page-checkout .radio-line-wrap .radio-line-item.active .radio {
  border-color: #9cb067;
}
body.page-checkout .radio-line-wrap .radio-line-item.active .radio:before {
  content: "";
  position: absolute;
  width: 12px;
  height: 12px;
  background-color: #9cb067;
  left: 2px;
  top: 2px;
  border-radius: 50%;
}
body.page-checkout .radio-line-wrap .radio-line-item .radio {
  position: relative;
  width: 18px;
  flex: 0 0 18px;
  height: 18px;
  border: 1px solid #d4d4d4;
  border-radius: 50%;
}
body.page-checkout .radio-line-wrap .radio-line-item .left {
  margin-right: 10px;
  display: flex;
  align-items: center;
  flex: 0 0 88px;
}
body.page-checkout .radio-line-wrap .radio-line-item .left img {
  width: 60px;
  margin-left: 10px;
}
body.page-checkout .radio-line-wrap .radio-line-item .right .title {
  font-weight: bold;
  margin-bottom: 10px;
}
body.page-checkout .addresses-wrap .item.address-right {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: auto;
  border: none;
}
@media (min-width: 768px) {
  body.page-checkout .addresses-wrap .item.address-right {
    padding: 0 2.6rem;
  }
}
@media (max-width: 768px) {
  body.page-checkout .addresses-wrap .item.address-right {
    padding: 0;
  }
  body.page-checkout .addresses-wrap .item.address-right button:first-of-type {
    margin-right: 10px;
  }
}
@media (min-width: 768px) {
  body.page-checkout .addresses-wrap .item.address-right {
    flex-direction: column;
  }
}
body.page-checkout .comment-wrap .commentTxt {
  border-color: #e5e5e5;
}
body.page-checkout .comment-wrap .commentTxt:focus-visible {
  border-color: #9cb067;
  outline: 0;
}

body.page-checkout .checkout-black, body.page-bk-stripe .checkout-black {
  margin-bottom: 2.6rem;
}
@media (max-width: 768px) {
  body.page-checkout .checkout-black, body.page-bk-stripe .checkout-black {
    margin-bottom: 1rem;
  }
}
body.page-checkout .checkout-title, body.page-bk-stripe .checkout-title {
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 16px;
  margin-bottom: 16px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
@media (max-width: 768px) {
  body.page-checkout .checkout-title, body.page-bk-stripe .checkout-title {
    padding-bottom: 10px;
    margin-bottom: 10px;
  }
}
body.page-checkout .checkout-title .btn, body.page-bk-stripe .checkout-title .btn {
  margin-bottom: -10px;
}
body.page-checkout .checkout-title .btn.icon, body.page-bk-stripe .checkout-title .btn.icon {
  font-size: 1rem;
}
@media (min-width: 768px) {
  body.page-checkout .total-wrap .card-body, body.page-bk-stripe .total-wrap .card-body {
    padding: 0;
  }
}
body.page-checkout .total-wrap .card-header, body.page-bk-stripe .total-wrap .card-header {
  background-color: #fff;
  margin-bottom: 1rem;
  border-bottom: 1px solid #E6E6E6;
}
@media (min-width: 768px) {
  body.page-checkout .total-wrap .card-header, body.page-bk-stripe .total-wrap .card-header {
    padding: 0 0 0.8rem;
  }
}
body.page-checkout .total-wrap .card-header h5, body.page-bk-stripe .total-wrap .card-header h5 {
  font-weight: bold;
}
body.page-checkout .total-wrap .card-header span, body.page-bk-stripe .total-wrap .card-header span {
  line-height: 24px;
  min-width: 24px;
  color: #fff;
  text-align: center;
}
body.page-checkout .total-wrap .products-wrap, body.page-bk-stripe .total-wrap .products-wrap {
  border-bottom: 1px solid #E6E6E6;
  margin-bottom: 1.3rem;
  padding-bottom: 0.3rem;
}
@media (min-width: 768px) {
  body.page-checkout .total-wrap .products-wrap, body.page-bk-stripe .total-wrap .products-wrap {
    max-height: 380px;
    overflow-y: auto;
  }
}
body.page-checkout .total-wrap .products-wrap .item, body.page-bk-stripe .total-wrap .products-wrap .item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.8rem;
}
body.page-checkout .total-wrap .products-wrap .item .image, body.page-bk-stripe .total-wrap .products-wrap .item .image {
  display: flex;
  align-items: center;
  padding-right: 4px;
}
body.page-checkout .total-wrap .products-wrap .item .image .quantity, body.page-bk-stripe .total-wrap .products-wrap .item .image .quantity {
  margin-left: 3px;
  color: #7a7a7a;
}
body.page-checkout .total-wrap .products-wrap .item .price, body.page-bk-stripe .total-wrap .products-wrap .item .price {
  color: #7a7a7a;
}
body.page-checkout .total-wrap .totals, body.page-bk-stripe .total-wrap .totals {
  padding-left: 0;
  list-style: none;
  margin-bottom: 0;
}
@media (min-width: 768px) {
  body.page-checkout .total-wrap .totals, body.page-bk-stripe .total-wrap .totals {
    padding-bottom: 0.3rem;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #E6E6E6;
  }
}
body.page-checkout .total-wrap .totals > li, body.page-bk-stripe .total-wrap .totals > li {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
}
body.page-checkout .total-wrap .totals > li:last-of-type, body.page-bk-stripe .total-wrap .totals > li:last-of-type {
  font-weight: bold;
}
@media (max-width: 768px) {
  body.page-checkout .total-wrap .totals > li:last-of-type, body.page-bk-stripe .total-wrap .totals > li:last-of-type {
    margin-bottom: 0;
  }
}
body.page-checkout .total-wrap .totals > li:last-of-type > span:last-of-type, body.page-bk-stripe .total-wrap .totals > li:last-of-type > span:last-of-type {
  color: #dc3545;
}
body.page-checkout .total-wrap .totals > li > span:first-of-type, body.page-bk-stripe .total-wrap .totals > li > span:first-of-type {
  font-size: 0.8rem;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:56:05
 */
@media (min-width: 992px) {
  body.page-categories .col-lg-9.right-column {
    flex: 1;
  }
}
@media (min-width: 992px) {
  body.page-categories .col-lg-3.left-column {
    max-width: 290px;
  }
}
@media (min-width: 1500px) {
  body.page-categories .col-lg-3.left-column {
    max-width: 340px;
  }
}
body.page-categories .col-lg-3.left-column .card:not(:last-of-type) {
  border-bottom: 1px solid #E6E6E6;
  margin-bottom: 1.4rem;
  padding-bottom: 1.4rem;
}
@media (max-width: 992px) {
  body.page-categories .left-column {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    cursor: pointer;
    z-index: 999;
    background: rgba(0, 0, 0, 0.4);
  }
  body.page-categories .left-column .x-fixed-top {
    position: absolute;
    top: 0;
    height: 100%;
    width: 80%;
    padding: 20px;
    text-align: left;
    overflow-y: auto;
    transform: translateX(100%);
    background-color: #fff;
    transition: all 0.3s ease-out;
    right: 0;
  }
  body.page-categories .left-column .x-fixed-top.active {
    transform: translateX(0);
  }
}
@media (max-width: 992px) {
  body.page-categories .children-wrap {
    display: none;
  }
}
body.page-categories .children-wrap a {
  color: #333;
  margin-right: 4px;
}
body.page-categories .children-wrap a:hover {
  color: #9cb067;
}
body.page-categories .style-wrap label {
  cursor: pointer;
}
body.page-categories .style-wrap label.active svg {
  fill: #9cb067;
}
body.page-categories .style-wrap label svg {
  fill: #999;
}
body.page-categories .filter-value-wrap .list-group {
  display: block;
}
body.page-categories .filter-value-wrap .list-group .list-group-item {
  display: inline-block;
  cursor: pointer;
  font-size: 12px;
  background: #f3f3f3;
  border: none;
  color: #666;
  padding: 4px 12px;
}
body.page-categories .filter-value-wrap .list-group .list-group-item.delete-all {
  background: #9cb067;
  color: #fff;
}
body.page-categories .filter-value-wrap .list-group .list-group-item:hover {
  background: #9cb067;
  color: #fff;
}
@media (min-width: 992px) {
  body.page-categories .product-tool .order-select {
    min-width: 170px;
  }
}
@media (max-width: 768px) {
  body.page-categories .product-tool .right-per-page {
    flex: 1;
    justify-content: space-between;
  }
  body.page-categories .product-tool .right-per-page .perpage-select {
    max-width: 60px;
    padding-right: 8px;
  }
  body.page-categories .product-tool .right-per-page .form-select {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    padding-left: 0.5rem;
    font-size: 0.7rem;
  }
}
body.page-categories .ui-widget-content {
  border-radius: 0;
  position: relative;
  border: none;
  margin-right: 4px;
  margin-bottom: 0;
  background: none;
  cursor: pointer;
}
body.page-categories .ui-widget-content .ui-widget-header {
  background: #9cb067;
  position: absolute;
  top: 50%;
  border-radius: 0;
  height: 3px;
  margin-top: -3px;
}
body.page-categories .ui-widget-content .slider-bg {
  background: #e3e3e3;
  position: absolute;
  top: 50%;
  width: 100%;
  height: 3px;
  margin-top: -3px;
}
body.page-categories .ui-widget-content .ui-slider-handle {
  width: 4px;
  margin-left: 0;
  cursor: ew-resize;
  border: none !important;
  border-radius: 0;
  background: #9cb067;
  outline: none !important;
}

.sidebar-widget {
  list-style: none;
  padding: 0 0 1.4rem;
  border-bottom: 1px solid #E6E6E6;
}
.sidebar-widget li {
  padding-top: 0.3rem;
  padding-bottom: 0.3rem;
  overflow: hidden;
  width: 100%;
  line-height: 24px;
}
.sidebar-widget li:last-of-type {
  border-bottom: none;
}
.sidebar-widget li .category-href {
  position: relative;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: calc(100% - 36px);
}
.sidebar-widget li.active > a {
  font-weight: bold;
  color: #9cb067;
}
.sidebar-widget ul {
  padding-left: 12px;
  list-style: none;
}
.sidebar-widget .toggle-icon {
  float: right;
  font-size: 16px;
  padding: 0;
  width: 24px;
  height: 24px;
  text-align: center;
}
.sidebar-widget .toggle-icon i {
  color: #aaa;
}
.sidebar-widget .toggle-icon.collapsed {
  transform: rotate(-180deg);
}

.filter-box .attribute-item .form-check-label {
  cursor: pointer;
}
.filter-box .attribute-item .form-check-label:hover .form-check-input {
  border-color: #9cb067;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-01 16:23:34
 * @LastEditTime  2022-09-16 20:55:44
 */
body.page-order-success .order-wrap .card-body.main-body, body.page-payment .order-wrap .card-body.main-body {
  padding: 3rem;
  padding-left: 7rem;
}
@media (max-width: 768px) {
  body.page-order-success .order-wrap .card-body.main-body, body.page-payment .order-wrap .card-body.main-body {
    padding: 1rem;
  }
}
body.page-order-success .order-wrap .card-body .order-top, body.page-payment .order-wrap .card-body .order-top {
  padding-bottom: 2.7rem;
  margin-bottom: 2.7rem;
  display: flex;
  justify-content: center;
}
body.page-order-success .order-wrap .card-body .order-top .left, body.page-payment .order-wrap .card-body .order-top .left {
  margin-top: -6px;
}
@media (max-width: 768px) {
  body.page-order-success .order-wrap .card-body .order-top .left, body.page-payment .order-wrap .card-body .order-top .left {
    display: none;
  }
}
body.page-order-success .order-wrap .card-body .order-top .left i, body.page-payment .order-wrap .card-body .order-top .left i {
  color: #4caf50;
  font-size: 80px;
  line-height: 1;
}
body.page-order-success .order-wrap .card-body .order-top .right, body.page-payment .order-wrap .card-body .order-top .right {
  flex: 1;
  margin-left: 2rem;
}
@media (max-width: 768px) {
  body.page-order-success .order-wrap .card-body .order-top .right, body.page-payment .order-wrap .card-body .order-top .right {
    margin: 0;
  }
}
body.page-order-success .order-wrap .card-body .order-top .right .table, body.page-payment .order-wrap .card-body .order-top .right .table {
  margin-bottom: 0;
}
body.page-order-success .order-wrap .card-body .order-top .right .order-title, body.page-payment .order-wrap .card-body .order-top .right .order-title {
  margin-bottom: 1.2rem;
  font-weight: 400;
}
body.page-order-success .order-wrap .card-body .order-top .right .order-info, body.page-payment .order-wrap .card-body .order-top .right .order-info {
  padding: 0.7rem;
  background-color: #fffaf0;
  border: 1px solid #ffe1ad;
}
body.page-order-success .order-wrap .card-body .order-bottom, body.page-payment .order-wrap .card-body .order-bottom {
  margin-left: calc(2rem + 80px);
  line-height: 2;
}
@media (max-width: 768px) {
  body.page-order-success .order-wrap .card-body .order-bottom, body.page-payment .order-wrap .card-body .order-bottom {
    margin: 0;
  }
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-01 16:23:34
 * @LastEditTime  2022-09-16 20:55:58
 */
@media (max-width: 768px) {
  body.page-account-order-list .order-status-wrap {
    margin-bottom: 14px;
  }
  body.page-account-order-list .order-status-wrap li {
    flex: 1;
    text-align: center;
  }
  body.page-account-order-list .order-status-wrap .nav-link {
    padding-left: 2px;
    padding-right: 2px;
  }
}
body.page-account-order-list .order-wrap .table .sep-row {
  height: 20px;
}
body.page-account-order-list .order-wrap .table .sep-row td {
  border: 0;
}
body.page-account-order-list .order-wrap .table .head-tr {
  background: #f5f5f5;
}
body.page-account-order-list .order-wrap .table .head-tr td {
  border-bottom-color: #f5f5f5;
}
body.page-account-order-list .order-wrap .table thead {
  background: #f5f5f5;
}
body.page-account-order-list .order-wrap .table thead th {
  font-weight: 500;
  border: 0;
}
body.page-account-order-list .order-wrap .table tbody td {
  border: 1px solid #e5e5e5;
}
body.page-account-order-list .order-wrap .table .product-info {
  display: flex;
  align-items: center;
}
body.page-account-order-list .order-wrap .table .product-info .img {
  flex: 0 0 60px;
  margin-right: 10px;
}
@media (max-width: 768px) {
  body.page-account-order-list .order-wrap .table .product-info .img {
    flex: 0 0 50px;
    width: 50px;
  }
}
@media (max-width: 768px) {
  body.page-account-order-list .order-wrap .table .product-info .name a {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    font-size: 12px;
  }
}
body.page-account-order-list .order-mb-wrap .order-mb-list {
  border-radius: 4px;
}
body.page-account-order-list .order-mb-wrap .header-wrapper {
  padding-bottom: 10px;
  margin-bottom: 12px;
  border-bottom: 1px solid #eee;
}
body.page-account-order-list .order-mb-wrap .footer-wrapper {
  padding-top: 10px;
  margin-top: 12px;
  border-top: 1px solid #eee;
}

body.page-account-order-info {
  background-color: #F7F8FA;
}
body.page-account-order-info .product-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  padding-bottom: 1rem;
  margin-bottom: 1rem;
}
body.page-account-order-info .product-list .left {
  flex: 0 0 80px;
  margin-right: 10px;
}
body.page-account-order-info .product-list .right {
  color: #767676;
}
@media (min-width: 768px) {
  body.page-account-order-info .product-list .right {
    font-size: 0.9375rem;
  }
}
body.page-account-order-info .product-list .right .name {
  margin-bottom: 0.4rem;
  color: #1a1a1a;
}
body.page-account-order-info .nowrap {
  white-space: nowrap;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-01 16:23:34
 * @LastEditTime  2022-09-16 20:55:54
 */
body.page-account-address .addresses-wrap .item, body.page-checkout .addresses-wrap .item {
  position: relative;
  padding: 14px;
  margin-bottom: 1.3rem;
  border: 1px solid #e5e5e5;
  height: 140px;
  cursor: pointer;
}
@media (max-width: 768px) {
  body.page-account-address .addresses-wrap .item, body.page-checkout .addresses-wrap .item {
    margin-bottom: 0.9rem;
    padding: 10px;
    height: auto;
  }
  body.page-account-address .addresses-wrap .item.address-right, body.page-checkout .addresses-wrap .item.address-right {
    margin-bottom: 0;
  }
}
body.page-account-address .addresses-wrap .item:hover, body.page-checkout .addresses-wrap .item:hover {
  border-color: #222;
}
body.page-account-address .addresses-wrap .item.active, body.page-checkout .addresses-wrap .item.active {
  border-left: none;
}
body.page-account-address .addresses-wrap .item.active:before, body.page-checkout .addresses-wrap .item.active:before {
  content: "";
  position: absolute;
  top: -1px;
  left: 0;
  width: 4px;
  height: calc(100% + 2px);
  background-color: #4991F4;
  background: repeating-linear-gradient(-45deg, #d60404 0, #d60404 10px, #fff 10px, #fff 20px, #4991F4 20px, #4991F4 30px, #fff 30px, #fff 40px);
}
body.page-account-address .addresses-wrap .item .name-wrap, body.page-checkout .addresses-wrap .item .name-wrap {
  margin-bottom: 0.6rem;
  line-height: 1;
}
body.page-account-address .addresses-wrap .item .name-wrap .name, body.page-checkout .addresses-wrap .item .name-wrap .name {
  font-size: 1rem;
  font-weight: bold;
}
body.page-account-address .addresses-wrap .item .name-wrap .phone, body.page-checkout .addresses-wrap .item .name-wrap .phone {
  font-size: 0.8rem;
  color: #666;
}
body.page-account-address .addresses-wrap .item .zipcode, body.page-checkout .addresses-wrap .item .zipcode {
  margin-bottom: 0.3rem;
  min-height: 20px;
}
body.page-account-address .addresses-wrap .item .address-info, body.page-checkout .addresses-wrap .item .address-info {
  height: 32px;
  line-height: 1.3;
}
body.page-account-address .addresses-wrap .item .address-bottom, body.page-checkout .addresses-wrap .item .address-bottom {
  min-height: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
body.page-account-address .mobileWidth, body.page-checkout .mobileWidth {
  width: 600px;
}
@media (max-width: 768px) {
  body.page-account-address .mobileWidth, body.page-checkout .mobileWidth {
    width: 95%;
  }
}
body.page-account-address .dialog-address, body.page-checkout .dialog-address {
  display: block;
}
@media (max-width: 768px) {
  body.page-account-address .dialog-address > div, body.page-checkout .dialog-address > div {
    width: 100%;
  }
}
@media (min-width: 768px) {
  body.page-account-address .dialog-address, body.page-checkout .dialog-address {
    display: flex;
  }
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-15 15:43:12
 * @LastEditTime  2022-09-16 20:56:17
 */
body.page-brands .brand-item {
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.07);
  margin-bottom: 10px;
  height: 133px;
  width: 100%;
}
body.page-brands .brand-item > img {
  max-height: 100%;
}
body.page-brands .brand-list li {
  list-style: none;
}
body.page-brands .brand-list li a {
  color: #242424;
  text-decoration: none;
}
body.page-brands .curser-list {
  cursor: pointer;
}
@media (max-width: 768px) {
  body.page-brands .curser-list {
    display: block;
  }
  body.page-brands .curser-list li {
    display: inline-block;
    margin-bottom: 6px;
  }
  body.page-brands .curser-list li + .list-group-item {
    border-left: 1px solid #e4e4e4;
  }
}
body.page-brands .curser-list > li > a {
  color: #242424;
  transition: all 0.3s;
  text-decoration: none;
}
body.page-brands .curser-list > li > a:hover {
  background-color: #eee;
}
body.page-brands .curser-list > li > a:active {
  background-color: #aaa;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:55:22
 */
.modules-box .module-item {
  position: relative;
}
.modules-box .module-item.module-item-design:hover .module-edit {
  display: flex;
}
.modules-box .module-item.module-item-design:hover:after {
  display: block;
}
.modules-box .module-item:after {
  position: absolute;
  bottom: -28px;
  left: 0;
  width: 100%;
  z-index: 9;
  align-items: center;
  justify-content: center;
  display: none;
}
.modules-box .module-item .module-edit {
  position: absolute;
  bottom: -28px;
  left: 0;
  width: 100%;
  z-index: 9;
  align-items: center;
  justify-content: center;
  display: none;
}
.modules-box .module-item .module-edit .edit-wrap {
  background-color: #9cb067;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20px;
  clip-path: polygon(0 0, 100% 0, 90% 100%, 10% 100%);
}
.modules-box .module-item .module-edit .edit-wrap > div {
  height: 28px;
  line-height: 30px;
  padding: 0 10px;
  font-size: 16px;
  cursor: pointer;
  position: relative;
}
.modules-box .module-item .module-edit .edit-wrap > div:first-of-type:after {
  content: "";
  position: absolute;
  top: 0;
  border: solid rgba(0, 0, 0, 0);
  border-right: solid #9cb067;
  border-width: 28px 10px 0 0;
  right: 100%;
}
.modules-box .module-item .module-edit .edit-wrap > div:first-of-type:hover:after {
  border-right-color: rgb(141.9428571429, 163.4493506494, 84.9506493506);
}
.modules-box .module-item .module-edit .edit-wrap > div:last-of-type:after {
  content: "";
  position: absolute;
  top: 0;
  border: solid rgba(0, 0, 0, 0);
  border-left: solid #9cb067;
  border-width: 28px 0 0 10px;
  left: 100%;
}
.modules-box .module-item .module-edit .edit-wrap > div:last-of-type:hover:after {
  border-left-color: rgb(141.9428571429, 163.4493506494, 84.9506493506);
}
.modules-box .module-item .module-edit .edit-wrap > div:hover {
  background-color: rgb(141.9428571429, 163.4493506494, 84.9506493506);
}

.module-image-plus .container-fluid {
  padding-right: 0;
  padding-left: 0;
}
.module-image-plus .module-image-plus-top {
  display: flex;
}
.module-image-plus .module-image-plus-top .right {
  margin-left: 20px;
}
.module-image-plus .module-image-plus-bottom {
  margin-top: 20px;
}

.module-image-banner .container-fluid {
  padding-right: 0;
  padding-left: 0;
}

.banner-magnify-hover .image-wrap-item {
  padding-left: 0;
  padding-right: 0;
}
.banner-magnify-hover .image-wrap-cont {
  display: flex;
  flex-wrap: wrap;
}
.banner-magnify-hover .image-wrap {
  overflow: hidden;
  display: block;
  position: relative;
  flex: 1;
}
.banner-magnify-hover .image-wrap .image-title-cont {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 1;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  color: #fff;
  text-align: center;
  padding-bottom: 30px;
  justify-content: flex-end;
  align-items: center;
}
.banner-magnify-hover .image-wrap .image-title-cont h2 {
  font-weight: 700;
  color: white;
  font-size: 40px;
}
.banner-magnify-hover .image-wrap .image-title-cont .description {
  color: white;
  font-size: 16px;
}
.banner-magnify-hover .image-wrap img {
  transition: 0.3s ease-in-out;
}
@media (max-width: 768px) {
  .banner-magnify-hover .image-wrap-item {
    padding-left: 10px;
    padding-right: 10px;
  }
  .banner-magnify-hover .image-wrap-cont {
    display: block;
  }
  .banner-magnify-hover .image-wrap {
    flex: none;
  }
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-04 17:52:22
 * @LastEditTime  2022-09-16 20:57:25
 */
.module-brand .brand-item {
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.07);
  margin-bottom: 10px;
  height: 120px;
  overflow: hidden;
  border: 1px solid transparent;
  transition: all 0.3s ease-in-out;
}
.module-brand .brand-item:hover {
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1);
  border: 1px solid #9cb067;
}
.module-brand .brand-item > img {
  max-height: 100%;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:57:33
 */
.module-tab-product .module-title {
  margin-bottom: 1rem;
}
.module-tab-product .nav .nav-link {
  color: #6c757d;
  font-size: 0.9rem;
}
.module-tab-product .nav .nav-link.active {
  color: #111;
  font-weight: bold;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:57:36
 */
.swiper.module-slideshow {
  --swiper-theme-color: #ff6600; /* 设置Swiper风格 */
  --swiper-navigation-color: #ff6600; /* 单独设置按钮颜色 */
  --swiper-navigation-size: 30px; /* 设置按钮大小 */
}
.swiper.module-slideshow .swiper-button-prev, .swiper.module-slideshow .swiper-button-next {
  display: none;
}
.swiper.module-slideshow:hover .swiper-button-prev, .swiper.module-slideshow:hover .swiper-button-next {
  display: block;
}

.swiper-style-plus {
  position: relative;
}
.swiper-style-plus .swiper-pagination {
  bottom: -10px !important;
}
.swiper-style-plus .swiper-button-prev, .swiper-style-plus .swiper-button-next {
  width: 34px;
  height: 37px;
  color: #999;
}
@media (max-width: 768px) {
  .swiper-style-plus .swiper-button-prev, .swiper-style-plus .swiper-button-next {
    display: none;
  }
}
.swiper-style-plus .swiper-button-prev:hover, .swiper-style-plus .swiper-button-next:hover {
  color: #9cb067;
}
.swiper-style-plus .swiper-button-prev:after, .swiper-style-plus .swiper-button-next:after {
  font-size: 26px;
}
.swiper-style-plus .swiper-button-prev {
  left: -40px;
}
.swiper-style-plus .swiper-button-next {
  right: -40px;
}
.swiper-style-plus .swiper-pagination .swiper-pagination-bullet-active {
  background: #9cb067;
}
.swiper-style-plus .swiper-pagination.rectangle span {
  border-radius: 0;
  height: 3px;
}

.module-slideshow-text {
  --swiper-theme-color: #111; /* 设置Swiper风格 */
  --swiper-navigation-color: #111; /* 单独设置按钮颜色 */
  --swiper-pagination-bullet-width: 10px; /* 设置分页器宽度 */
  --swiper-pagination-bullet-height: 10px; /* 设置分页器高度 */
}
@media (min-width: 992px) {
  .module-slideshow-text .swiper-pagination {
    bottom: 20px !important;
  }
}
.module-slideshow-text .content-wrap {
  display: flex;
  position: absolute;
  left: 0;
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
}
.module-slideshow-text .content-wrap.start {
  justify-content: flex-start;
}
.module-slideshow-text .content-wrap.center {
  justify-content: center;
  text-align: center;
}
.module-slideshow-text .content-wrap.center .text-wrap {
  max-width: 800px;
}
.module-slideshow-text .content-wrap.end {
  justify-content: flex-end;
  text-align: right;
}
.module-slideshow-text .image-wrap {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  height: 100%;
  display: flex;
  align-items: center;
}
.module-slideshow-text .text-wrap {
  max-width: 600px;
}
@media (max-width: 768px) {
  .module-slideshow-text .text-wrap {
    max-width: 65%;
  }
}
.module-slideshow-text .text-wrap .sub-title {
  font-size: 1rem;
  margin-bottom: 1.4rem;
  color: #333;
}
@media (min-width: 992px) {
  .module-slideshow-text .text-wrap .sub-title {
    font-size: 1.5rem;
  }
}
@media (max-width: 768px) {
  .module-slideshow-text .text-wrap .sub-title {
    margin-bottom: 0.5rem;
    font-size: 0.7rem;
  }
}
.module-slideshow-text .text-wrap .title {
  font-size: 1.4rem;
  margin-bottom: 1.6rem;
  color: #333;
}
@media (min-width: 992px) {
  .module-slideshow-text .text-wrap .title {
    font-size: 3rem;
  }
}
@media (max-width: 768px) {
  .module-slideshow-text .text-wrap .title {
    margin-bottom: 0.6rem;
    font-size: 0.9rem;
  }
}
.module-slideshow-text .text-wrap .description {
  font-size: 0.8rem;
  color: #666;
}
@media (min-width: 992px) {
  .module-slideshow-text .text-wrap .description {
    font-size: 1rem;
    margin-bottom: 2rem;
  }
}
@media (max-width: 768px) {
  .module-slideshow-text .text-wrap .description {
    display: none;
  }
}
.module-slideshow-text .text-wrap a.btn {
  margin-top: 1.5rem;
  font-size: 1rem;
  padding: 0.8rem 2.2rem;
  border: 2px solid #e0e0e0;
  background-color: #fff;
}
@media (max-width: 768px) {
  .module-slideshow-text .text-wrap a.btn {
    margin-top: 0rem;
    font-size: 0.7rem;
    padding: 0.2rem 0.6rem;
  }
}
.module-slideshow-text .text-wrap a.btn:hover {
  background-color: #9cb067;
  color: #fff;
  border-color: #9cb067;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-04 17:52:22
 * @LastEditTime  2022-09-16 20:57:25
 */
.banner-text-wrap:hover .left-image .detail-image {
  transform: translate(-50%, -50%) scale(1.1);
}
.banner-text-wrap .container-fluid {
  padding: 0;
}
.banner-text-wrap .container-fluid .row {
  margin: 0;
}
.banner-text-wrap .container-fluid .row > div {
  padding: 0;
}
.banner-text-wrap .left-image {
  position: relative;
}
.banner-text-wrap .left-image .detail-image {
  position: absolute;
  width: 38%;
  top: 50%;
  left: 100%;
  transition: all 0.3s ease-in-out;
  transform: translate(-50%, -50%);
}
@media (max-width: 768px) {
  .banner-text-wrap .left-image .detail-image {
    display: none;
  }
}
.banner-text-wrap .right-text {
  display: flex;
  align-items: center;
  justify-content: center;
}
@media (min-width: 768px) {
  .banner-text-wrap .right-text {
    padding-left: 9% !important;
  }
}
.banner-text-wrap .right-text .description {
  margin-bottom: 20px;
}
.banner-text-wrap .right-text .text-wrap {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
@media (min-width: 768px) {
  .banner-text-wrap .right-text .text-wrap {
    width: 80%;
  }
}
@media (max-width: 768px) {
  .banner-text-wrap .right-text .text-wrap {
    padding-top: 26px;
    padding-bottom: 26px;
  }
}
.banner-text-wrap .right-text .btn-view-details {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-04 17:52:22
 * @LastEditTime  2022-09-16 20:57:25
 */
.module-icon-item:hover p {
  color: #9cb067 !important;
}

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-02 19:19:52
 * @LastEditTime  2022-09-16 20:57:29
 */
.pages-wrap {
  transition: all 0.3s ease-in-out;
}
.pages-wrap:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}
.pages-wrap:hover .page-info .pages-title a {
  color: #9cb067;
}
.pages-wrap .image {
  margin-bottom: 10px;
}
.pages-wrap .image a {
  display: block;
}
.pages-wrap .page-info {
  transition: all 0.3s ease-in-out;
  padding: 0 5px 10px;
}
.pages-wrap .pages-title {
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.pages-wrap .pages-title a {
  color: #222;
  transition: all 0.3s ease-in-out;
}
.pages-wrap .pages-summary {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  height: 40px;
  color: #888;
  margin-bottom: 10px;
}

/**
 * @copyright     2023 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2023-12-04 13:52:45
 * @LastEditTime  2023-12-04 13:52:53
 */
.module-image-402 .image-402-title {
  text-align: center;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 14px;
}
.module-image-402 .image-402-sub-title {
  text-align: center;
  margin-bottom: 30px;
  font-size: 0.9rem;
}
@media (max-width: 768px) {
  .module-image-402 .image-402-sub-title {
    margin-bottom: 10px;
  }
}
.module-image-402 .module-image-info {
  margin: 0 -10px;
}
@media (max-width: 768px) {
  .module-image-402 .module-image-info {
    margin: 0 -5px;
  }
}
.module-image-402 .module-image-info > div {
  padding: 0 10px;
}
@media (max-width: 768px) {
  .module-image-402 .module-image-info > div {
    padding: 0 5px;
  }
}
.module-image-402 .module-image-info .image-402-1 {
  grid-area: 1/1/3/2;
}
@media (max-width: 768px) {
  .module-image-402 .module-image-info .image-402-1 {
    margin-bottom: 10px;
  }
}
.module-image-402 .module-image-info .image-402-2 {
  grid-area: 1/2/2/3;
}
@media (max-width: 768px) {
  .module-image-402 .module-image-info .image-402-2 {
    grid-area: 3/1/4/2;
  }
}
.module-image-402 .module-image-info .image-402-3 {
  grid-area: 2/2/3/3;
}
@media (min-width: 768px) {
  .module-image-402 .module-image-info .image-402-3 {
    margin-top: 20px;
  }
}
@media (max-width: 768px) {
  .module-image-402 .module-image-info .image-402-3 {
    grid-area: 1/2/2/3;
    margin-bottom: 10px;
  }
}
.module-image-402 .module-image-info .image-402-4 {
  grid-area: 1/3/3/4;
}
@media (max-width: 768px) {
  .module-image-402 .module-image-info .image-402-4 {
    grid-area: 2/2/4/3;
  }
}
.module-image-402 .image-wrap {
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}
.module-image-402 .image-wrap .img-name {
  position: absolute;
  bottom: 28px;
  left: 0;
  display: flex;
  justify-content: center;
  width: 100%;
}
@media (max-width: 768px) {
  .module-image-402 .image-wrap .img-name {
    bottom: 18px;
  }
}
.module-image-402 .image-wrap .img-name span {
  color: #111;
  font-weight: bold;
  padding: 6px 10px;
  background-color: #fff;
  border-radius: 40px;
  font-size: 1rem;
  box-shadow: 0 5px 30px rgba(0, 0, 0, 0.1);
}
@media (max-width: 768px) {
  .module-image-402 .image-wrap .img-name span {
    font-size: 0.7rem;
  }
}

/**
 * @copyright     2023 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2023-12-04 13:52:45
 * @LastEditTime  2023-12-04 13:52:53
 */
.design-image-301 {
  width: 100%;
  height: auto;
  overflow: hidden;
  position: relative;
  clear: both;
  text-align: left;
}
.design-image-301 > a {
  display: block;
  line-height: 100%;
  position: relative;
  transition: all 0.2s ease-in-out 0s;
}
.design-image-301 > a:hover:before {
  background-color: rgba(0, 0, 0, 0.1);
  border: 15px solid rgba(255, 255, 255, 0.5);
  opacity: 1;
}
.design-image-301 > a:before {
  box-sizing: border-box;
  border: 0px solid rgba(0, 0, 0, 0.3);
  bottom: 0;
  opacity: 0;
  content: "";
  left: 0;
  overflow: visible;
  position: absolute;
  z-index: 9;
  right: 0;
  top: 0;
  transition: all 0.2s ease-in-out 0s;
}
.design-image-301 > a:nth-of-type(1) {
  width: 49%;
}
.design-image-301 > a:nth-of-type(2), .design-image-301 > a:nth-of-type(3) {
  position: absolute;
  right: 0;
  width: 49%;
}
.design-image-301 > a:nth-of-type(2) {
  top: 0;
}
.design-image-301 > a:nth-of-type(3) {
  bottom: 0 !important;
}

/**
 * @copyright     2024 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2024-11-12 10:42:54
 * @LastEditTime  2024-11-12 10:43:15
 */
.module-img-text-slideshow {
  --swiper-theme-color: #fff; /* 设置Swiper风格 */
  --swiper-navigation-color: #fff; /* 单独设置按钮颜色 */
  --swiper-pagination-bullet-width: 60px; /* 设置分页器宽度 */
  --swiper-pagination-bullet-height: 4px; /* 设置分页器高度 */
  --swiper-pagination-bullet-inactive-opacity: 1;
  overflow: hidden;
  position: relative;
}
.module-img-text-slideshow .swiper-pagination-bullet {
  border-radius: 2px;
  background-color: rgba(255, 255, 255, 0.4);
  position: relative;
  overflow: hidden;
}
.module-img-text-slideshow .swiper-pagination-bullet span {
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: #fff;
}
@media (min-width: 992px) {
  .module-img-text-slideshow .swiper-pagination {
    bottom: 20px !important;
  }
}
.module-img-text-slideshow .swiper-slide-active .image-wrap {
  transform: scale(1);
}
.module-img-text-slideshow .autoplay-progress {
  position: absolute;
  right: 16px;
  bottom: 16px;
  z-index: 10;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--swiper-theme-color);
}
.module-img-text-slideshow .autoplay-progress svg {
  --progress: 0;
  position: absolute;
  left: 0;
  top: 0px;
  z-index: 10;
  width: 100%;
  height: 100%;
  stroke-width: 4px;
  stroke: var(--swiper-theme-color);
  fill: none;
  stroke-dashoffset: calc(125.6px * (1 - var(--progress)));
  stroke-dasharray: 125.6;
  transform: rotate(-90deg);
}
.module-img-text-slideshow .content-wrap {
  display: flex;
}
.module-img-text-slideshow .content-wrap.start {
  justify-content: flex-start;
}
.module-img-text-slideshow .content-wrap.center {
  justify-content: center;
  text-align: center;
}
.module-img-text-slideshow .content-wrap.center .text-wrap {
  max-width: 800px;
}
.module-img-text-slideshow .content-wrap.end {
  justify-content: flex-end;
  text-align: right;
}
.module-img-text-slideshow .image-wrap {
  height: 330px;
  background-repeat: no-repeat;
  background-position: center center;
  transform-origin: center center;
  background-size: cover;
  display: flex;
  align-items: center;
  transform: scale(1.1732);
  transition: transform 3s cubic-bezier(0.23, 1, 0.32, 1);
}
@media (min-width: 768px) {
  .module-img-text-slideshow .image-wrap {
    height: 600px;
  }
}
@media (min-width: 1200px) {
  .module-img-text-slideshow .image-wrap {
    height: 700px;
  }
}
.module-img-text-slideshow .text-wrap {
  max-width: 600px;
  color: #fff;
}
@media (min-width: 992px) {
  .module-img-text-slideshow .text-wrap {
    margin-top: 30px;
  }
}
@media (max-width: 992px) {
  .module-img-text-slideshow .text-wrap {
    padding: 30px 0;
  }
}
.module-img-text-slideshow .text-wrap .sub-title {
  font-size: 0.8rem;
  margin-bottom: 0.8rem;
}
@media (min-width: 992px) {
  .module-img-text-slideshow .text-wrap .sub-title {
    font-size: 2rem;
  }
}
.module-img-text-slideshow .text-wrap .title {
  font-size: 1rem;
  margin-bottom: 0.7rem;
  color: #fff;
}
@media (min-width: 992px) {
  .module-img-text-slideshow .text-wrap .title {
    font-size: 3rem;
  }
}
@media (min-width: 1200px) {
  .module-img-text-slideshow .text-wrap .title {
    font-size: 4rem;
  }
}
.module-img-text-slideshow .text-wrap .description {
  font-size: 1rem;
}
@media (min-width: 992px) {
  .module-img-text-slideshow .text-wrap .description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
  }
}
.module-img-text-slideshow .text-wrap a.btn {
  font-size: 0.8rem;
  font-weight: bold;
  background-color: #fff;
  border: 1px solid #222;
}
.module-img-text-slideshow .text-wrap a.btn:hover {
  background-color: #222;
}
@media (min-width: 992px) {
  .module-img-text-slideshow .text-wrap a.btn {
    padding: 1rem 2rem;
    margin-top: 1.5rem;
  }
}

.module-swiper-img-scroll-text {
  padding: 36px 0;
  width: 100%;
  font-size: 20px;
  overflow: hidden;
  position: relative;
}
@media (max-width: 768px) {
  .module-swiper-img-scroll-text {
    font-size: 14px !important;
    padding: 16px 0 !important;
  }
}

.scroll-info {
  white-space: nowrap;
}

.scroll-info .scroll-text {
  padding: 0 15px;
  font-weight: bold;
  display: inline-block;
  animation: marquee-animation 3s linear infinite;
}

@keyframes marquee-animation {
  0% {
    transform: translate(0);
  }
  100% {
    transform: translate(-100%); /* 滚动到左侧 */
  }
}
/**
 * @copyright     2024 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2024-11-12 10:42:54
 * @LastEditTime  2024-11-12 10:43:15
 */
.img-text-banner-wrap .text-wrap {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  height: 100%;
}
@media (min-width: 1200px) {
  .img-text-banner-wrap .text-wrap {
    padding: 40px 70px;
  }
}
@media (max-width: 992px) {
  .img-text-banner-wrap .text-wrap {
    align-items: center;
  }
}
.img-text-banner-wrap .text-wrap .title {
  margin-bottom: 20px;
}
@media (min-width: 992px) {
  .img-text-banner-wrap .text-wrap .title {
    font-size: 34px;
  }
}
.img-text-banner-wrap .text-wrap .description {
  line-height: 1.7;
  margin-bottom: 20px;
}
@media (min-width: 992px) {
  .img-text-banner-wrap .text-wrap .description {
    margin-bottom: 30px;
    font-size: 15px;
  }
}

.module-image-403 {
  display: flex;
  align-items: center;
}
.module-image-403 .img-cont {
  width: 50%;
}
.module-image-403 .img-cont img {
  width: 100%;
}
.module-image-403 .img-cont.left {
  order: 1;
  text-align: left;
}
.module-image-403 .img-cont.right {
  order: 2;
  text-align: right;
}
.module-image-403 .text-template {
  width: 50%;
}
.module-image-403 .text-template.left {
  padding-left: 30px;
  order: 2;
}
.module-image-403 .text-template.right {
  padding-right: 30px;
  order: 1;
}
@media (max-width: 768px) {
  .module-image-403 {
    flex-direction: column;
  }
  .module-image-403 .img-cont, .module-image-403 .text-template {
    width: 100%;
    order: 0 !important;
  }
  .module-image-403 .text-template.left, .module-image-403 .text-template.right {
    padding: 20px 0;
  }
}

.module-title {
  display: flex;
  padding-bottom: 0;
  justify-content: center;
}

.module-title::after {
  content: none;
}

.module-title span {
  position: relative;
}

.module-title span::after {
  position: absolute;
  bottom: -15px;
  transform: translateX(-50%);
  left: 50%;
  content: "";
  width: 60px;
  height: 2px;
  background: #9cb067;
}

.module-title span.line-none::after {
  content: none;
}

.product-wrap .product-name {
  color: #9cb067;
  font-size: var(--productSize);
  height: auto;
}

.product-module .product-wrap .image {
  border-bottom: none;
}

.product-wrap .product-bottom-info {
  text-align: center;
}

.product-wrap .product-price .price-new {
  font-size: 1rem;
}

.product-module .is_more {
  margin: 0 auto;
  margin-top: 20px;
  width: -moz-fit-content;
  width: fit-content;
  font-size: 16px;
  background-color: #9cb067;
}
.product-module .is_more a {
  padding: 7px 25px;
  display: block;
  color: white;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}
.product-module .is_more a::after {
  background-image: linear-gradient(90deg, transparent, hsla(0, 0%, 100%, 0.25), transparent);
  content: "";
  height: 100%;
  left: 150%;
  position: absolute;
  top: 0;
  transform: skew(-20deg);
  width: 200%;
}
.product-module .is_more a:hover::after {
  animation: leftLine 0.75s cubic-bezier(0.01, 0.56, 1, 1);
}
@keyframes leftLine {
  100% {
    left: -200%;
  }
}

.banner-view-module {
  position: relative;
}
.banner-view-module .banner-container {
  padding: 0;
}
.banner-view-module .banner-content {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.banner-view-module .banner-images {
  display: flex;
  width: 100%;
  height: 100%;
  max-height: 100vh;
}
.banner-view-module .banner-image {
  flex: 1;
}
.banner-view-module .banner-image img,
.banner-view-module .banner-image video {
  width: 100%;
}
.banner-view-module .banner-text-overlay {
  position: absolute;
  bottom: 10%;
  left: 0;
  right: 0;
  margin: 0 auto;
  padding: 0 8%;
  text-align: center;
  color: #fff;
  z-index: 2;
}
.banner-view-module .banner-text-overlay h1 {
  font-size: 4rem;
  font-weight: normal;
  margin-bottom: 1.5rem;
  color: #fff;
}
.banner-view-module .banner-text-overlay h5 {
  color: #fff;
  margin: 20px 0;
}
.banner-view-module .banner-text-overlay .btn {
  background-color: transparent;
  color: #fff;
  padding: 0.75rem 2rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  transition: all 0.3s ease;
  border: none;
}
.banner-view-module .banner-text-overlay .btn:hover {
  text-decoration: underline;
}
@media (max-width: 768px) {
  .banner-view-module .banner-images {
    flex-direction: column;
  }
  .banner-view-module .banner-text-overlay h1 {
    font-size: 2.5rem;
  }
}

.banner-view-module::before {
  content: "";
  position: absolute;
  left: 0px;
  top: 0px;
  background-color: rgba(0, 0, 0, 0.3294117647);
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.right_move_cont-module {
  padding: 80px 0;
  opacity: 0;
  margin-bottom: -30px;
  transition: all 0.3s;
}
.right_move_cont-module.is-visible {
  opacity: 1;
  margin-bottom: 0px;
}
.right_move_cont-module.is-visible .right_move_cont-content .right_move_cont-left .right_move_cont-title h2 {
  opacity: 1;
  transform: translateY(0);
}
.right_move_cont-module.is-visible .right_move_cont-content .right_move_cont-left .right_move_cont-des p {
  opacity: 1;
  transform: translateY(0);
}
.right_move_cont-module.is-visible .right_move_cont-content .right_move_cont-left .right_move_cont-button a {
  opacity: 1;
  transform: translateY(0);
}
.right_move_cont-module .right_move_cont-content {
  display: flex;
  align-items: center;
  margin: 0 auto;
  padding: 0 50px;
}
.right_move_cont-module .right_move_cont-content .no-data {
  width: 100%;
  text-align: center;
  padding: 80px 0;
  font-size: 18px;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-left {
  width: 35%;
  padding-right: 80px;
  flex-shrink: 0;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-left .right_move_cont-title h2 {
  font-family: "Serif", "Times New Roman", Times, serif;
  font-size: 48px;
  font-weight: 400;
  margin-bottom: 20px;
  line-height: 1.2;
  opacity: 0;
  transition: all 0.3s;
  transform: translateY(30px);
  color: #9cb067;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-left .right_move_cont-des p {
  font-size: 16px;
  margin-bottom: 40px;
  line-height: 1.6;
  opacity: 0;
  transition: all 0.3s;
  transform: translateY(30px);
  color: #9cb067;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-left .right_move_cont-button a {
  display: inline-block;
  padding: 15px 45px;
  border: 1px solid #9cb067;
  text-decoration: none;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: uppercase;
  opacity: 0;
  transition: all 0.3s;
  transform: translateY(30px);
}
.right_move_cont-module .right_move_cont-content .right_move_cont-left .right_move_cont-button a:hover {
  background-color: #9cb067;
  color: white;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right {
  width: 65%;
  overflow: hidden;
  position: relative;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right .swiper-container {
  width: 100%;
  padding-bottom: 40px;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right .swiper-slide {
  width: 280px;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right .swiper-slide img {
  display: block;
  width: 100%;
  height: auto;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right .swiper-slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.3) 100%);
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right .swiper-slide:hover::before {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.5) 100%);
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right .swiper-scrollbar {
  background: #e1dcd2;
  height: 2px;
  bottom: 0;
  left: 0;
  width: 100%;
}
.right_move_cont-module .right_move_cont-content .right_move_cont-right .swiper-scrollbar .swiper-scrollbar-drag {
  background: #9cb067;
}
@media (max-width: 992px) {
  .right_move_cont-module .right_move_cont-content {
    padding: 0 30px;
  }
  .right_move_cont-module .right_move_cont-content .right_move_cont-left {
    width: 40%;
    padding-right: 40px;
  }
  .right_move_cont-module .right_move_cont-content .right_move_cont-left .right_move_cont-title h2 {
    font-size: 36px;
  }
  .right_move_cont-module .right_move_cont-content .right_move_cont-right {
    width: 60%;
  }
}
@media (max-width: 768px) {
  .right_move_cont-module {
    padding: 40px 0;
  }
  .right_move_cont-module .right_move_cont-content {
    flex-direction: column;
    padding: 0 20px;
  }
  .right_move_cont-module .right_move_cont-content .right_move_cont-left {
    width: 100%;
    padding-right: 0;
    text-align: center;
    margin-bottom: 40px;
  }
  .right_move_cont-module .right_move_cont-content .right_move_cont-left .right_move_cont-title h2 {
    font-size: 32px;
  }
  .right_move_cont-module .right_move_cont-content .right_move_cont-right {
    width: 100%;
  }
}

.product-view-pro-module {
  padding: 2rem 0;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-tabs .nav .nav-link {
  cursor: pointer;
  padding: 0.5rem 1rem;
  color: #999;
  border-bottom: 2px solid transparent;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-tabs .nav .nav-link.active {
  color: #9cb067;
  border-bottom-color: #9cb067;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-swiper {
  overflow: hidden;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-swiper .swiper-container {
  width: 100%;
  height: 100%;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-swiper .swiper-slide {
  width: auto;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-swiper .swiper-scrollbar {
  position: relative;
  margin: 2rem auto 0;
  width: 200px;
  height: 2px;
  background-color: #eee;
}
.product-view-pro-module .product-view-pro-content .product-view-pro-swiper .swiper-scrollbar .swiper-scrollbar-drag {
  background-color: #9cb067;
}
@media (max-width: 992px) {
  .product-view-pro-module .product-view-pro-content .product-view-pro-swiper .swiper-slide {
    width: 33.33%;
  }
}
@media (max-width: 768px) {
  .product-view-pro-module .product-view-pro-content .product-view-pro-swiper .swiper-slide {
    width: 50%;
  }
}

.product-pro {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.product-pro .image {
  position: relative;
  overflow: hidden;
}
.product-pro .image .product-tab-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 3;
  background-color: #fff;
  color: #000;
  padding: 2px 8px;
  font-size: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 2px;
  text-transform: uppercase;
  pointer-events: none;
}
.product-pro .image .image-old img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.product-pro .image .image-change,
.product-pro .image .button-wrap,
.product-pro .image .product-skus-wrapper {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}
.product-pro .image.is-hover .image-change,
.product-pro .image.is-hover .button-wrap,
.product-pro .image.is-hover .product-skus-wrapper {
  opacity: 1;
  visibility: visible;
}
.product-pro .image .image-change {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1;
  pointer-events: none;
}
.product-pro .image .image-change .arrow {
  cursor: pointer;
  width: 30px;
  height: 30px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
}
.product-pro .image .image-change .arrow.left {
  margin-left: 10px;
}
.product-pro .image .image-change .arrow.right {
  margin-right: 10px;
}
.product-pro .image .image-change .arrow::after {
  content: "";
  border: solid #fff;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
}
.product-pro .image .image-change .arrow.left::after {
  transform: rotate(135deg);
}
.product-pro .image .image-change .arrow.right::after {
  transform: rotate(-45deg);
}
.product-pro .image .button-wrap {
  position: absolute;
  top: -50px; /* 为SKU区域留出空间 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  gap: 10px;
  pointer-events: none;
}
.product-pro .image .button-wrap .btn {
  pointer-events: auto;
}
.product-pro .image .product-skus-wrapper {
  position: absolute;
  bottom: 0px;
  z-index: 2;
  width: 100%;
  background-color: #ffffff;
}
.product-pro .image .product-skus-wrapper .swiper-wrapper {
  flex-wrap: wrap;
}
.product-pro .image .product-skus-wrapper .swiper-slide {
  width: 70px;
}
.product-pro .image .product-skus-wrapper .product-sku-item {
  display: block;
  padding: 5px 0;
  width: 100%;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.product-pro .image .product-skus-wrapper .product-sku-item:hover {
  background-color: rgb(229.5, 229.5, 229.5);
}
.product-pro .image .product-skus-wrapper .product-sku-item.disabled {
  background-color: #f8f8f8;
  color: #ccc;
  cursor: not-allowed;
  text-decoration: line-through;
}
.product-pro .product-bottom-info {
  padding: 1rem 1rem;
}
.product-pro .product-bottom-info .product-brand {
  font-size: 14px;
  color: #888;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
}
.product-pro .product-bottom-info .product-name {
  font-size: 14px;
  color: #000;
  margin-bottom: 0.5rem;
}
.product-pro .product-bottom-info .product-price .price-new {
  font-size: 14px;
  color: #000;
  font-weight: bold;
}
.product-pro .product-bottom-info .product-price .price-old {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
  margin-left: 0.5rem;
}
@media (max-width: 768px) {
  .product-pro .image .image-change .arrow {
    width: 20px;
    height: 20px;
  }
}

.maijiaxiu-cont {
  padding: 20px 0 60px 0;
}
.maijiaxiu-cont .title-wrapper {
  margin-bottom: 2rem;
}
.maijiaxiu-cont .title-wrapper .title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #9cb067;
}
.maijiaxiu-cont .title-wrapper .title-line {
  display: inline-block;
  width: 50px;
  height: 2px;
  color: #9cb067;
}
.maijiaxiu-cont .maijiaxiu-swiper {
  height: 850px;
}
.maijiaxiu-cont .maijiaxiu-swiper .swiper-container {
  width: 100%;
  height: 100%;
}
.maijiaxiu-cont .maijiaxiu-swiper .swiper-slide {
  width: auto;
  height: 100%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.maijiaxiu-cont .maijiaxiu-swiper .swiper-slide .slide-image {
  height: 100%;
}
.maijiaxiu-cont .maijiaxiu-swiper .swiper-slide .slide-image img {
  width: auto;
  height: 100%;
  transition: transform 0.3s ease;
}
.maijiaxiu-cont .maijiaxiu-swiper .swiper-slide .user-info {
  position: absolute;
  bottom: 15px;
  left: 15px;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.4);
  padding: 5px 10px;
  border-radius: 20px;
  color: #fff;
}
.maijiaxiu-cont .maijiaxiu-swiper .swiper-scrollbar {
  position: relative;
  margin: 2rem auto 0;
  width: 200px;
  height: 2px;
  background-color: #eee;
}
.maijiaxiu-cont .maijiaxiu-swiper .swiper-scrollbar .swiper-scrollbar-drag {
  background-color: #9cb067;
}
@media (max-width: 768px) {
  .maijiaxiu-cont .maijiaxiu-swiper {
    height: 500px;
  }
}
.maijiaxiu-cont .maijiaxiu-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1050;
}
.maijiaxiu-cont .maijiaxiu-modal .modal-content {
  background-color: #fdfcf9;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}
.maijiaxiu-cont .maijiaxiu-modal .modal-header {
  padding: 20px 60px 20px 20px;
  border-bottom: 1px solid #eee;
  flex-shrink: 0;
}
.maijiaxiu-cont .maijiaxiu-modal .close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 28px;
  font-weight: lighter;
  cursor: pointer;
  line-height: 1;
  z-index: 10;
}
.maijiaxiu-cont .maijiaxiu-modal .modal-body {
  overflow-y: auto;
  flex-grow: 1;
  padding: 2rem;
}
.maijiaxiu-cont .maijiaxiu-modal .modal-body .product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}
.maijiaxiu-cont .maijiaxiu-modal .modal-body .product-grid .product-pro {
  width: 100%;
}

.product-pro {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.product-pro .image {
  position: relative;
  overflow: hidden;
}
.product-pro .image .product-tab-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 3;
  background-color: #fff;
  color: #000;
  padding: 2px 8px;
  font-size: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 2px;
  text-transform: uppercase;
  pointer-events: none;
}
.product-pro .image .image-old img {
  width: 100%;
  height: auto;
  aspect-ratio: 4/7;
  -o-object-fit: cover;
     object-fit: cover;
}
.product-pro .image .image-change,
.product-pro .image .button-wrap,
.product-pro .image .product-skus-wrapper {
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s, visibility 0.3s;
}
.product-pro .image.is-hover .image-change,
.product-pro .image.is-hover .button-wrap,
.product-pro .image.is-hover .product-skus-wrapper {
  opacity: 1;
  visibility: visible;
}
.product-pro .image .image-change {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 1;
  pointer-events: none;
}
.product-pro .image .image-change .arrow {
  cursor: pointer;
  width: 30px;
  height: 30px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
}
.product-pro .image .image-change .arrow.left {
  margin-left: 10px;
}
.product-pro .image .image-change .arrow.right {
  margin-right: 10px;
}
.product-pro .image .image-change .arrow::after {
  content: "";
  border: solid #fff;
  border-width: 0 2px 2px 0;
  display: inline-block;
  padding: 3px;
}
.product-pro .image .image-change .arrow.left::after {
  transform: rotate(135deg);
}
.product-pro .image .image-change .arrow.right::after {
  transform: rotate(-45deg);
}
.product-pro .image .button-wrap {
  position: absolute;
  top: -50px; /* 为SKU区域留出空间 */
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  display: flex;
  gap: 10px;
  pointer-events: none;
}
.product-pro .image .button-wrap .btn {
  pointer-events: auto;
}
.product-pro .image .product-skus-wrapper {
  position: absolute;
  bottom: 0px;
  z-index: 2;
  width: 100%;
  background-color: #fdfcf9;
}
.product-pro .image .product-skus-wrapper .swiper-wrapper {
  flex-wrap: wrap;
}
.product-pro .image .product-skus-wrapper .swiper-slide {
  width: 70px;
}
.product-pro .image .product-skus-wrapper .product-sku-item {
  display: block;
  padding: 5px 0;
  width: 100%;
  background-color: #fdfcf9;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.product-pro .image .product-skus-wrapper .product-sku-item:hover {
  background-color: rgb(240.25, 232.875, 210.75);
}
.product-pro .image .product-skus-wrapper .product-sku-item.disabled {
  background-color: #f8f8f8;
  color: #ccc;
  cursor: not-allowed;
  text-decoration: line-through;
}
.product-pro .product-bottom-info {
  padding: 1rem 1rem;
}
.product-pro .product-bottom-info .product-brand {
  font-size: 14px;
  color: #888;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
}
.product-pro .product-bottom-info .product-name {
  font-size: 14px;
  color: #000;
  margin-bottom: 0.5rem;
}
.product-pro .product-bottom-info .product-price .price-new {
  font-size: 14px;
  color: #000;
  font-weight: bold;
}
.product-pro .product-bottom-info .product-price .price-old {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
  margin-left: 0.5rem;
}
@media (max-width: 768px) {
  .product-pro .image .image-change .arrow {
    width: 20px;
    height: 20px;
  }
}
