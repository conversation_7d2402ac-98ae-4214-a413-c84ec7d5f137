<template id="module-editor-image404-template">
    <div class="module-editor-image404-template">
        <div class="module-editor-row">{{ __('admin/builder.text_set_up') }}</div>
        <div class="module-edit-group">
            <div class="module-edit-title">{{ __('admin/builder.text_module_title') }}</div>
            <text-i18n v-model="form.title"></text-i18n>
        </div>
        <div class="module-editor-row">{{ __('admin/builder.page_product_general_settings') }}</div>
        <div class="module-edit-group">
            <div class="module-edit-title">{{ __('admin/builder.text2') }}</div>
            <el-select clearable v-model="form.center" placeholder="{{ __('admin/builder.text2') }}">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="module-edit-group">
            <div class="module-edit-title">{{ __('admin/builder.text6') }}</div>
            <el-switch v-model="form.is_more"></el-switch>
            <link-selector v-if="form.is_more" v-model="form.link"></link-selector>
        </div>
        <div class="module-edit-group">
            <div class="module-edit-title">{{ __('admin/builder.text1') }}</div>
            <el-switch v-model="form.is_title_line"></el-switch>
        </div>
        <div class="module-edit-group">
            <div class="module-edit-title">{{ __('admin/builder.text7') }}</div>
            <el-slider :min="12" :max="100" v-model="form.product_size"></el-slider>
        </div>
        <div class="module-edit-group">
            <div class="module-edit-title">{{ __('admin/builder.modules_quantity') }}</div>
            <el-input type="number" v-model="module.limit" size="small"></el-input>
        </div>
    </div>
</template>

<script type="text/javascript">
    Vue.component('module-editor-image404', {
        delimiters: ['${', '}'],
        template: '#module-editor-image404-template',
        props: ['module'],
        data: function() {
            return {
                keyword: '',
                productData: [],
                loading: null,
                form: null,
                options: [{
                        value: 'center',
                        label: '{{ __('admin/builder.text3') }}',
                    },
                    {
                        value: 'left',
                        label: '{{ __('admin/builder.text4') }}',
                    },
                    {
                        value: 'right',
                        label: '{{ __('admin/builder.text5') }}',
                    }
                ]
            }
        },

        watch: {
            form: {
                handler: function(val) {
                    this.$emit('on-changed', val);
                },
                deep: true
            },
        },

        created: function() {
            this.form = JSON.parse(JSON.stringify(this.module));
        },

        computed: {},

        methods: {}
    });
</script>

@push('footer-script')
    <script>
        register = @json($register);

        register.make = {
            style: {
                background_color: ''
            },
            floor: languagesFill(''),
            limit: 4,
            title: languagesFill('{{ __('admin/builder.text_module_title') }}'),
            center: 'center',
            is_more: false,
            is_title_line: true,
            link: {
                type: 'product',
                value: '',
                product_size: 14,
            },
        };

        app.source.modules.push(register)
    </script>
@endpush
