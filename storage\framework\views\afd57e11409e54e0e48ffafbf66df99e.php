<?php echo plugin_setting('google_analytics.code'); ?>


<script>
  const currency = '<?php echo e(current_currency_code()); ?>';

  $(document).ready(function () {
    // 商品列表商品加 购物车 和 收藏
    $("button.btn-add-cart, button.btn-wishlist").click(function () {
      const id = $(this).attr('product-id');
      const price = $(this).attr('product-price');
      const productName = $(this).parents('.product-wrap').find('.product-name').text();
      const event = $(this).hasClass('btn-add-cart') ? 'add_to_cart' : 'add_to_wishlist';
      gtag("event", event, {
        currency: currency,
        value: price * 1,
        items: [
          {
            item_id: id,
            item_name: productName,
            price: price,
            quantity: 1
          }
        ]
      });
    });

    // add to cart
    $(".quantity-btns button.add-cart").click(function () {
      const id = $(this).attr('product-id');
      const price = $(this).attr('product-price');
      const productName = $('.product-name').text();
      const qty = $('input[name="quantity"]').val();
      gtag("event", "add_to_cart", {
        currency: currency,
        value: price * qty,
        items: [
          {
            item_id: id,
            item_name: productName,
            price: price,
            quantity: qty
          }
        ]
      });
    });

    //search
    $(".offcanvas-header input").on("keyup", function (event) {
      if (event.which === 13) {
        gtag("event", "search", {
          search_term: $(this).val()
        });
      }
    });
  })
</script>
<?php /**PATH D:\shopleadeCont\git\saas\plugins/GoogleAnalytics/Views/layout_header_code.blade.php ENDPATH**/ ?>