<?php
/**
 * ShopifyImportService.php
 *
 * @copyright  2024 beikeshop.com - All Rights Reserved
 * @link       https://beikeshop.com
 * <AUTHOR> <<EMAIL>>
 * @created    2024-12-12 16:09:21
 * @modified   2024-12-12 16:09:21
 */

namespace Plugin\ShopifyImport\Services;

use AWS\CRT\Log;
use Beike\Models\Category;
use Beike\Models\CategoryDescription;
use GuzzleHttp\Client;
use Beike\Admin\Services\ProductService;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class ShopifyImportService
{
    private $languages;

    private string $defaultLanguage = 'en';

    private $client;

    private int $count_one_time = 5; // 一次请求处理的商品数量


    public function __construct()
    {
        $this->languages = array_column(locales(), 'code');
        if (!in_array($this->defaultLanguage, $this->languages)) {
            $this->defaultLanguage = system_setting('base.locale');
        }
        $apiKey = plugin_setting('shopify_import.consumer_key');
        $password = plugin_setting('shopify_import.access_token');
        $accessToken = plugin_setting('shopify_import.access_token'); //'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';

        $shopDomain = plugin_setting('shopify_import.url');//'wintotest.myshopify.com';
        $this->client = new Client([
            'base_uri' => 'https://' . $shopDomain . '/admin/api/2024-10/',
//            'auth' => [$apiKey, $password],
            'headers' => [
                'X-Shopify-Access-Token' => $accessToken, // 设置Access Token作为请求头
                'Content-Type' => 'application/json', // 设置请求的内容类型
            ],
            'timeout' => 30.0,
            'debug' => false, // 设置为true以启用调试模式
        ]);
    }

    public static function getInstance(): self
    {
        return new self;
    }


    private function getProducts($sinceId = null)
    {
        $pageCount = 250;

        $response = $this->client->request('GET', 'products.json', [
            'query' => [
                'since_id' => $sinceId,
                'limit' => $pageCount, // 可选参数，设置返回的产品数量限制
                "status"=>"ACTIVE",
            ],
        ]);

        if ($response->getStatusCode() === 200) {
            $products = json_decode($response->getBody(), true);
        } else {
            $errorMsg = 'Failed to retrieve product list. Status code: ' . $response->getStatusCode() . PHP_EOL;
            $error = json_decode($response->getBody(), true);
            if (isset($error['error'])) {
                $errorMsg .= '; Error: ' . $error['error'] . PHP_EOL;
            }
            throw new \Exception($errorMsg);
        }

        if ($products['products'] && count($products['products']) == $pageCount) {
            $nextPageProducts = $this->getProducts($products['products'][$pageCount - 1]['id']);
            return array_merge($nextPageProducts, $products['products']);
        }

        return $products['products'];
    }

    private function getCategories()
    {
        return $this->client->get('products/categories', ['per_page' => 100]);
    }

    private function getVariantions($productId)
    {
        $result = [];
        $page = 1;
        while ($variantions = $this->client->get('products/' . $productId . '/variations', ['per_page' => 100, 'page' => $page])) {
            $page++;
            $result = array_merge($result, $variantions);
        }
        return $result;
    }

    private function groupById($data)
    {
        $result = [];

        foreach ($data as $datum) {
            $result[$datum['id']] = $datum;
        }

        return $result;
    }

    /**
     * 传入一个image的url，将图片下载到本地，然后返回本地图片的路径
     * @param $imageUrl
     * @return string
     */
    private function getRemoteImage($imageUrl)
    {
        $response = Http::get($imageUrl);
        if (!$response->successful()) {
            throw (new \Exception('get image failed from url: ' . $imageUrl));
        }

        $parsedUrl = parse_url($imageUrl);
        $path = $parsedUrl['path'];
        Storage::disk('catalog')->put('store-' . current_store_id() . '/shopify/' . $path, $response->body());

        return 'catalog/store-' . current_store_id(). '/shopify' . $path;
    }

    /**
     * 传入variables和sku的规格名称（如：黑色, L），返回规格值数组（如：[2,1]）
     * @param $variables
     * @param $skuVariantName
     * @return array
     */
    private function getSkuVariants($variables, $skuVariantName)
    {
        $result = [];

        $variants = explode(' / ', $skuVariantName);
        foreach ($variants as $index => $variant) {
            $variant = trim($variant);
            foreach ($variables[$index]['values'] as $i => $value) {
                if ($value['name'][$this->defaultLanguage] == $variant) {
                    $result[$index] = $i;
                    break;
                }
            }
        }

        return $result;
    }

    private function getFormatedProducts($data)
    {
        $data = $this->groupById($data);
        $cateIds = Category::get()->pluck('id')->toArray();

        foreach ($data as $item) {

            $product_type = $item['product_type'];
            $cateList = CategoryDescription::whereIn('category_id', $cateIds)
                ->where('name', $product_type)->get()->pluck('category_id')->toArray();
            if(empty($cateList)) {
                throw new \Exception("商品类型不存在：{$product_type}".json_encode($item));
            }

            $cateList = array_unique($cateList);


//            dd($data, $item);
            if (!isset($result[$item['id']])) {
                $description = [
                    'name' => $item['title'] ?? '',
                    'content' => $item['body_html'] ?? '',
                    'meta_title' => $item['title'] ?? '',
                    'meta_description' => '',
                    'meta_keywords' => '',
                ];

                $variables = [];
//                if (count($item['options']) != 1 && count($item['options'][0]['values']) != 1) {
                if (count($item['options']) != 1) {
                    foreach ($item['options'] as $variableItem) {
                        $variables[] = [
                            'name' => $this->multiLangage($variableItem['name']),
                            'isImage' => 0,
                            'values' => array_map(function ($o) {
                                return [
                                    'name' => $this->multiLangage($o),
                                    'image' => ''
                                ];
                            }, $variableItem['values'])
                        ];
                    }
                }

                $skus = [];
                foreach ($item['variants'] as $index => $variantion) {
                    $variants = [];
                    if ($variables) {
                        $variants = $this->getSkuVariants($variables, $variantion['title']);
                    }

                    $isDefault = $index == 0;

                    $skuImages = [];
                    if ($variantion['image_id']) {
                        $image = collect($item['images'])->firstWhere('id', $variantion['image_id']);
                        if ($image) {
                            $skuImages[] = $this->getRemoteImage($image['src']);
                        }
                    }

                    $skus[] = [
                        'variants' => $variants,
                        'images' => $skuImages,
                        'model' => $variantion['sku'],
                        'sku' => $variantion['sku'] ? $variantion['sku'] . '-' . $variantion['id'] : 'shopify-' . $variantion['id'],
                        'price' => $variantion['price'],
                        'origin_price' => $variantion['compare_at_price'],
                        'cost_price' => 0,
                        'quantity' => $variantion['inventory_quantity'],
                        'is_default' => $isDefault,
                    ];
                }


                $result[$item['id']] = [
                    'categories' => $cateList,
                    'brand_id' => 0,
                    'images' => array_map(function ($img) {
                        return $this->getRemoteImage($img['src']);
                    }, $item['images']),
                    'slug' => $item['handle'],
                    'video' => '',
                    'weight' => $item['variants'][0]['weight'],
                    'weight_class' => $item['variants'][0]['weight_unit'],//Weight::DEFAULT_CLASS,
                    'position' => 0,
                    'active' => $item['status'] == 'active',
                    'descriptions' => $this->multiLangage($description),
                    'variables' => json_encode($variables),
                    'skus' => $skus,
                ];
            }
        }

        return $result;
    }

    public function init()
    {
        $data = $this->getProducts();

        cache()->set('bk_shopify_products' . current_user()->id, $data);

        $count = ceil(count($data) / $this->count_one_time);

        return $count;
    }

    public function import($page): void
    {
        $data = cache()->get('bk_shopify_products' . current_user()->id);

        $data = array_slice($data, $this->count_one_time * ($page - 1), $this->count_one_time, true);
        $data = $this->getFormatedProducts($data);


        foreach ($data as $item) {
            (new ProductService())->create($item);
        }
    }

    private function multiLangage($description)
    {
        $descriptions = [];
        foreach ($this->languages as $language) {
            $descriptions[$language] = $description;
        }
        return $descriptions;
    }
}
