<footer>
   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("footer.before",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("footer.services.before",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>

  <?php if($footer_content['services']['enable']): ?>
    <div class="services-wrap">
      <div class="container-fluid">
        <div class="row align-items-lg-center">
          <?php $__currentLoopData = $footer_content['services']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-lg-3 col-md-6 col-6">
              <div class="service-item my-1">
                <div class="icon"><img src="<?php echo e(image_resize($item['image'], 80, 80)); ?>" class="img-fluid"></div>
                <div class="text">
                  <p class="title"><?php echo e($item['title'][locale()] ?? ''); ?></p>
                  <p class="sub-title"><?php echo e($item['sub_title'][locale()] ?? ''); ?></p>
                </div>
              </div>
            </div>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
      </div>
    </div>
  <?php endif; ?>
   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("footer.services.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>

  <div class="container-fluid">
    <div class="footer-content">
      <div class="row">
        <div class="col-12 col-md-3 me-lg-5">
          <div class="footer-content-left footer-link-wrap">
            <h6 class="text-uppercase intro-title"><?php echo e(__('shop/common.company_profile')); ?><span class="icon-open"><i class="bi bi-plus-lg"></i></span></h6>
            <div class="intro-wrap">
              <?php if($footer_content['content']['intro']['logo'] ?? false): ?>
                <div class="logo"><a href="<?php echo e(shop_route('home.index')); ?>"><img src="<?php echo e(image_origin($footer_content['content']['intro']['logo'])); ?>" alt="<?php echo e(system_setting('base.meta_title', 'BeikeShop开源好用的跨境电商系统')); ?>" class="img-fluid"></a></div>
              <?php endif; ?>
              <div class="text tinymce-format-p"><?php echo $footer_content['content']['intro']['text'][locale()] ?? ''; ?></div>
              <div class="social-network">
                <?php $__currentLoopData = $footer_content['content']['intro']['social_network'] ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <a href="<?php echo e($item['link']); ?>" target="_blank"><img src="<?php echo e(image_origin($item['image'])); ?>" class="img-fluid"></a>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </div>
            </div>
          </div>
        </div>
        <?php for($i = 1; $i <= 3; $i++): ?>
          <?php
            $link = $footer_content['content']['link' . $i];
          ?>
          <?php if($design || ($link['title'][locale()] ?? false)): ?>
          <div class="col-12 col-md footer-content-link<?php echo e($i); ?> footer-link-wrap">
            <h6 class="text-uppercase"><?php echo e($link['title'][locale()] ?? ''); ?><span class="icon-open"><i class="bi bi-plus-lg"></i></span></h6>
            <ul class="list-unstyled">
              <?php $__currentLoopData = $link['links']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if($item['link']): ?>
                <li class="lh-lg mb-2">
                  <a href="<?php echo e($item['link']); ?>" <?php if(isset($item['new_window']) && $item['new_window']): ?> target="_blank" <?php endif; ?>>
                    <?php echo e($item['text']); ?>

                  </a>
                </li>
              <?php endif; ?>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
          </div>
          <?php endif; ?>
        <?php endfor; ?>

         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("footer.contact.before",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
         <?php
                    $__hook_name="footer.contact";
                    ob_start();
                ?>
        <div class="col-12 col-md-3 footer-content-contact footer-link-wrap">
          <h6 class="text-uppercase"><?php echo e(__('common.contact_us')); ?><span class="icon-open"><i class="bi bi-plus-lg"></i></span> </h6>
          <ul class="list-unstyled">
            <?php if($footer_content['content']['contact']['email']): ?>
              <li class="lh-lg mb-2"><i class="bi bi-envelope-fill"></i> <?php echo e($footer_content['content']['contact']['email']); ?></li>
            <?php endif; ?>
            <?php if($footer_content['content']['contact']['telephone']): ?>
              <li class="lh-lg mb-2"><i class="bi bi-telephone-fill"></i> <?php echo e($footer_content['content']['contact']['telephone']); ?></li>
            <?php endif; ?>
            <?php if($footer_content['content']['contact']['address'][locale()] ?? ''): ?>
              <li class="lh-lg mb-2"><i class="bi bi-geo-alt-fill"></i> <?php echo e($footer_content['content']['contact']['address'][locale()] ?? ''); ?></li>
            <?php endif; ?>
          </ul>
        </div>
         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>
         <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("footer.contact.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
      </div>
    </div>
  </div>

   <?php
                    $__hook_name="footer.copyright";
                    ob_start();
                ?>
  <div class="footer-bottom">
    <div class="container-fluid">
      <div class="d-lg-flex align-items-center">
        <div class="text-center d-lg-flex justify-content-center">
          <!-- 删除版权信息, 请先购买授权 https://beikeshop.com/vip/subscription -->
          <?php if(!check_license()): ?>
          Powered By&nbsp;<a href="https://beikeshop.com/" target="_blank" rel="noopener">BeikeShop</a>&nbsp;-&nbsp;
          <?php endif; ?>
          <?php echo $footer_content['bottom']['copyright'][locale()] ?? ''; ?>

        </div>
        <?php if(isset($footer_content['bottom']['image']) && $footer_content['bottom']['image']): ?>
          <div class="ms-auto right-img py-md-2 text-center">
            <img src="<?php echo e(image_origin($footer_content['bottom']['image'])); ?>" class="img-fluid">
          </div>
        <?php endif; ?>
      </div>
    </div>
  </div>
   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                $__hook_content = ob_get_clean();
                $output = \Hook::getWrapper("$__hook_name",["data"=>$__definedVars],function($data) { return null; },$__hook_content);
                unset($__hook_name);
                unset($__hook_content);
                if ($output)
                echo $output;
                ?>

   <?php
                $__definedVars = (get_defined_vars()["__data"]);
                if (empty($__definedVars))
                {
                    $__definedVars = [];
                }
                
                $output = \Hook::getHook("footer.after",["data"=>$__definedVars],function($data) { return null; });
                if ($output)
                echo $output;
                ?>
</footer>
<?php /**PATH D:\shopleadeCont\git\saas\themes\fashion5/layout/footer.blade.php ENDPATH**/ ?>