@charset "UTF-8";

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-09-03 22:32:29
 * @LastEditTime  2022-09-16 20:48:00
 */

footer {
  background: $footerbg;
  margin-top: 0rem;
  position: relative;
  overflow: hidden;

  .footer-wrapper {
    position: relative;
    z-index: 1;
  }

  .footer-active {
    outline: 2px dashed #4bb1f0 !important;
  }

  .services-wrap {
    padding: 2.2rem 0;
    border-bottom: 1px solid #e7e7e7;
    justify-content: center;
    display: flex;
    border-top: 1px solid #e7e7e7;

    @media (max-width: 768px) {
      padding: 0.5rem 0;
    }
    .row{
      width: 100%;
      display: flex;
      justify-content: center;
    }
    
    .service-item {
      display: flex;
      align-items: center;
      justify-content: center;

      .icon {
        width: 30px;
        flex: 0 0 30px;
      
        margin-right: 14px;

        @media (max-width: 768px) {
          display: none;
        }
      }

      p {
        margin-bottom: 0;
      }

      .title {
        margin-bottom: 4px;
        font-weight: bold;
        font-size: 0.9rem;
        color: #333;
        white-space: nowrap;
      }

      .sub-title {
        font-size: 0.7rem;
        color: #8d94a0;
      }
    }
  }

  .footer-content {
    .footer-logo{
      display: flex;
      align-items: center;
    }
    @media (min-width: 768px) {
      padding: 3rem 7%;
    }

    > .row {
      @media (max-width: 768px) {
        margin: 0;

        > .col-12 {
          padding-left: 0;
          padding-right: 0;
        }
      }
    }

    .footer-content-left {
      width: 100%;
     

      .logo{
         padding-top: 10px;
        width: 100% !important;
        max-width: none;
      }
      .footer-logo-img{
        display: block;
         text-align: center;
         img{
          max-width: 240px;
         }
      }
   
    }

    a {
      color: #000000;

      &:hover {
        color: $primary;
      }
    }

    .logo {
      max-width: 240px;
      margin-bottom: 10px;
    }

    .text {
      color: #010000;
    }

    h6 {
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      @media (min-width: 768px) {
        margin-bottom: 16px;
      }

      @media (max-width: 768px) {
        margin-bottom: 0;
        padding: 12px 0;
      }

      
    }

    .social-network {
      margin-top: 10px;
      display: flex;

      > a {
        margin-right: 10px;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        &:hover {
          transform: translateY(-5px);
        }
        img{
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .intro-title {
      @media (min-width: 768px) {
        display: none;
      }
    }

    .footer-link-wrap {
      @media (max-width: 768px) {
        border-bottom: 1px solid #eee;
        padding: 0 7% !important;
      }

      &.active {
        .icon-open i:before {
          content: "\F63B";
        }

        ul.list-unstyled,
        .intro-wrap {
          max-height: 1666px;
        }
      }

      ul.list-unstyled,
      .intro-wrap {
        margin-bottom: 0;

        @media (max-width: 768px) {
          width: 100%;
          max-height: 0;
          overflow: hidden;
          transition: all 0.35s;
        }
      }
    }
  }

  .footer-bottom {
    display: flex;
    align-items: center;
    background: $footerbg;
    color: #666;

    @media (min-width: 768px) {
      min-height: 60px;
    }

    @media (max-width: 768px) {
      padding: 10px 0;
      .row.align-items-center,
      .d-flex {
        justify-content: center;
      }
    }
  }
}
