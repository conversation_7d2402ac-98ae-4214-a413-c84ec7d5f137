<?php if(isset($data)): ?>
    <script>
        ttq.track('ViewContent', {
            "contents": [
                {
                    "content_id": "<?php echo e(data_get($data, 'content_id')); ?>", // string. ID of the product. Example: "1077218".
                    "content_type": "product", // string. Either product or product_group.
                    "content_name": "<?php echo e(data_get($data, 'content_name')); ?>" // string. The name of the page or product. Example: "shirt".
                }
            ],
            "value": '<?php echo e(data_get($data, 'value')); ?>', // number. Value of the order or items sold. Example: 100.
            "currency": '<?php echo e(data_get($data, 'currency')); ?>' // string. The 4217 currency code. Example: "USD".
        })
        ;
    </script>
<?php endif; ?>
<?php /**PATH D:\shopleadeCont\git\saas\plugins/TikTokPixel/Views/view_content.blade.php ENDPATH**/ ?>