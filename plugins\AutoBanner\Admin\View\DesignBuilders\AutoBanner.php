<?php

namespace Plugin\AutoBanner\Admin\View\DesignBuilders;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class AutoBanner extends Component
{
    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return View
     */
    public function render(): View
    {
        $data['register'] = [
            'code'      => 'auto_banner',
            'sort'      => 0,
            'name'      => trans('AutoBanner::common.name'),
            'icon'      => 'https://oss.shopleade.com/saas/module/config_auto_text_icon.png',
            'view_path' => 'AutoBanner::shop/design_module_auto_banner',
        ];

        return view('AutoBanner::admin/design_module_auto_banner', $data);
    }
}
