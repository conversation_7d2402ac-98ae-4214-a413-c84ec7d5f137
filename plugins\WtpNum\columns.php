<?php
/**
 * Wtp 字段
 *
 * @copyright  2024 beikeshop.com - All Rights Reserved
 * @link       https://beikeshop.com
 * <AUTHOR> <<EMAIL>>
 * @created    2024-05-13 21:16:23
 * @modified   2024-05-13 21:16:23
 */

return [
    [
        'name'        => 'merchant_id',
        'label_key'   => 'common.merchant_id',
        'type'        => 'string',
        'required'    => true,
        'rules'       => 'required',
    ],
    [
        'name'        => 'md5_key',
        'label_key'   => 'common.md5_key',
        'type'        => 'string',
        'required'    => true,
        'rules'       => 'required',
    ],
    [
        'name'        => 'api_key',
        'label_key'   => 'common.api_key',
        'type'        => 'string',
        'required'    => true,
        'rules'       => 'required|min:32',
    ],
    [
        'name'        => 'api',
        'label_key'   => 'common.api',
        'type'        => 'select',
        'options'     => [
            ['value' => 'test', 'label_key' => 'common.api_test'],
            ['value' => 'live', 'label_key' => 'common.api_live'],
        ],
        'required'    => true,
    ],
    [
        'name'        => 'payment_type',
        'label_key'   => 'common.payment_type',
        'type'        => 'checkbox',
        'options'     => [
            ['value' => 'wdd', 'label_key' => 'common.wdd'],
        ],
        'required'    => true,
    ],
    [
        'name'        => 'log',
        'label'       => '日志',
        'type'        => 'select',
        'options'     => [
            ['value' => '1', 'label_key' => 'common.enable'],
            ['value' => '0', 'label_key' => 'common.disable'],
        ],
        'required'    => true,
    ],
];
