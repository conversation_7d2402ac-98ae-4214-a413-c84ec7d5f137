@charset "UTF-8";

/**
 * @copyright     2022 beikeshop.com - All Rights Reserved.
 * @link          https://beikeshop.com
 * <AUTHOR> shuo <<EMAIL>>
 * @Date          2022-08-24 17:24:33
 * @LastEditTime  2022-09-16 20:56:21
 */

body.page-product {
  @media (max-width: 768px) {
    background-color: #f2f2f3;
    padding-bottom: 64px;

    .breadcrumb-wrap {
      display: none
    }
  }

  #product-app.container {
    @media (max-width: 768px) {
      padding: 0;
      overflow-x: hidden;
    }
  }

  .product-mb-block {
    @media (max-width: 768px) {
      background-color: #fff;
      margin-bottom: 10px;
      padding: 10px;
    }
  }

  #product-description {
    img {
      max-width: 100%;
      height: auto;
    }
  }

  .product-image {
    position: relative;

    #swiper {
      height: 250px;
      @media (min-width: 480px) {
        height: 400px;
      }
      @media (min-width: 768px) {
        height: 500px;
      }

      &:hover {
        .swiper-pager > div {
          @media (max-width: 768px) {
            display: none;
          }
          background-color: rgba(255, 255, 255, 0.548);
          opacity: 1;
          &:hover {
            background-color: rgba(255, 255, 255);
          }
        }
      }
    }

    #swiper-mobile {
      width: 100%;
      border-right: 1px solid #eee;

      .swiper-pagination {
        --swiper-theme-color: #ff6600;/* 设置Swiper风格 */
        --swiper-navigation-color: #ff6600;/* 单独设置按钮颜色 */
        --swiper-navigation-size: 30px;/* 设置按钮大小 */
      }
    }

    .swiper-main-container {
      position: relative;
      border: 1px solid #eee;

      // 移除自定义放大镜样式
      #magnifier-lens,
      #magnifier-pane {
        display: none !important;
      }

      // ElevateZoom样式优化
      .zoomContainer {
        .zoomLens {
          border: 1px solid rgba(255, 255, 255, 0.8) !important;
          background-color: rgba(255, 255, 255, 0.2) !important;
          box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1) !important;
        }
        
        .zoomWindow {
          border: 1px solid #ddd !important;
          box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;
          border-radius: 2px !important;
          background-color: #fff !important;
        }
      }

      // 确保容器不会被放大镜影响
      .swiper {
        position: relative;
        z-index: 1;
      }

      #swiper-main .swiper-slide {
        display: flex;
        align-items: center;
        justify-content: center;
        
        img {
          max-width: 100%;
          height: auto;
          display: block;
          max-height: 600px !important;
        }
      }

      .swiper-button-next, .swiper-button-prev {
        color: #333;
        background-color: rgba(255, 255, 255, 0.8);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        top: 50%;
        transform: translateY(-50%);
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 10;

        &::after {
          font-size: 1.2rem;
        }
      }

      &:hover {
        .swiper-button-next, .swiper-button-prev {
          opacity: 1;
        }
      }
    }

    .swiper-thumbs-container {
      #swiper-thumbs {
        .swiper-slide {
          cursor: pointer;
          border: 1px solid #eee;
          opacity: 0.6;
          transition: opacity 0.3s;

          &.swiper-slide-thumb-active {
            opacity: 1;
            border: 2px solid #000;
          }
        }
      }
    }

    #product-video {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      z-index: 99;
      display: none;
    }

    .open-video {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      transform: translateX(-50%);
      left: 50%;
      z-index: 99;
      line-height: 1;
      cursor: pointer;

      &:hover {
        i {
          color: #fff;
          background-color: rgba(0, 0, 0, 0.648);
        }
      }

      i {
        font-size: 4rem;
        line-height: 1;
        border-radius: 50%;
        font-weight: 400;
        display: inline-block;
        color: rgba(255, 255, 255, 0.948);
        background-color: rgba(0, 0, 0, 0.348);

        @media (max-width: 768px) {
          font-size: 3rem;
        }
      }
    }

    .close-video {
      position: absolute;
      top: 6px;
      right: 10px;
      z-index: 9999;
      color: #aaa;
      font-size: 30px;
      cursor: pointer;
      &:hover {
        color: #fff;
      }
    }
  }

  .stock-and-sku {
    @media (max-width: 768px) {
      background: #fafafa;
      padding: 8px;
      line-height: 1.6;
    }

    > div {
      @media (min-width: 768px) {
        font-size: 14px;
        margin-bottom: 10px;
      }

      @media (max-width: 768px) {
        display: inline-block;
        margin-right: 10px;
      }

      .title {
        @media (min-width: 768px) {
          width: 80px;
        }
      }
    }
  }

  .variables-wrap {
    .variable-group {
      margin-bottom: 10px;

      &:last-of-type {
        margin-bottom: 0;
      }
    }

    .variable-info {
      > div {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.5rem;
        margin-bottom: 0.5rem;
        border: 1px solid #ddd;
        margin-left: 0;
        min-width: 3rem;
        cursor: pointer;
        text-align: center;
        font-weight: bold;
        flex-direction: column;
        border-radius: 4px;
        transition: all .1s ease-in-out;
        &:hover, &.selected {
          border-color: #222;
        }

        &:not(.is-v-image) {
          padding: 0.4rem 0.5rem;
        }

        > span.image {
          width: 50px;
          // margin-bottom: 6px;
        }

        &:not(.selected) {
          &.disabled {
            border: 1px dashed #2e2929;
            color: #999;
            font-weight: initial;
          }
        }
      }
    }
  }

  .product-btns {
    @media (max-width: 768px) {
      z-index: 100;
      max-height: 102px;
      display: flex;
      align-items: center;
      left: 0;
      right: 0;
      width: 100%;
      background: #fff;
      position: fixed;
      bottom: 0;
      box-shadow: 0 -8px 12px 0 rgba(0,0,0,.10196078431372549);
      flex-wrap: wrap;
      padding: 10px 10px calc(10px + env(safe-area-inset-bottom));

      .quantity-btns {
        flex: 1;
        display: flex;
      }

      .btn-buy-now {
        display: none;
      }

      .add-cart {
        flex: 1;
        margin: 0 10px;
        background-color: #212529;
        color: #fff;
      }

      .add-wishlist {
        .btn {
          padding: 0;
          color: #333 !important;
        }

        span {
          display: none;
        }

        i {
          font-size: 20px;
        }
      }
    }
  }

  .peoduct-info {
    .product-name {
      font-size: 1.7rem;
      line-height: 1.3;
      font-weight: 600;

      @media (max-width: 768px) {
        font-size: 1rem;
        font-weight: normal;
      }
    }

    .rating-wrap {
      margin-bottom: 2rem;

      .rating {
        margin-right: .5rem;
        i {
          color: $primary;
        }
      }
    }

    .price-wrap {
      margin-bottom: 2.4rem;

      @media (max-width: 768px) {
        margin-bottom: 1rem;
      }
    }

    .quantity-btns {
      .btn-buy-now {
        background-color: $primary;
        border-color: $primary;
      }
      @media (min-width: 768px) {
        display: flex;
      }

      .quantity-input {
        max-width: 5rem;
        text-align: center;
      }

      .quantity-wrap {
        height: 43px;
      }
    }
  }

  .product-description {
    .nav-tabs {
      .nav-link {
        border: none;

        &.active {
          position: relative;
          background-color: transparent;
          color: $primary;

          &:before {
            border-top: 1px solid $primary;
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
          }
        }
      }
    }
  }

  .attribute-table {
    tr {
      td:first-of-type {
        @media (min-width: 768px) {
          width: 20%;
        }

        @media (max-width: 768px) {
          width: 40%;
        }
      }
    }
  }

  .relations-wrap {
    .container {
      @media (max-width: 768px) {
        padding: 0 0 10px;
      }
    }

    .title {
      font-size: 20px;
      margin-bottom: 22px;

      @media (max-width: 768px) {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
      }
    }

    .swiper-pagination {
      bottom: -10px;

      .swiper-pagination-bullet {
        height: 3px;
        border-radius: 0;
      }
    }
  }
}

.magnifier-zoom-window {
  img {
    object-fit: contain !important;
    width: auto !important;
    height: auto !important;
    max-width: none !important;
    max-height: none !important;
  }
}

.magnifier-container {
  .magnifier-lens {
    border-radius: 2px;
  }
}
